import requests
import time
import json
import base64
from typing import Dict, List, Optional, Union, Any


class Maidalv:
    """
    Client for interacting with the Maidalv API for product IP checks.
    """
    
    def __init__(self, base_url: str, api_key: str):
        """
        Initialize the Maidalv API client.
        
        Args:
            base_url: The base URL of the Maidalv API (e.g., "https://api.maidalv.com")
            api_key: Your API key for authentication
        """
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
    
    def submit_check(self, 
                    main_product_image: str,
                    description: str,
                    ip_keywords: List[str],
                    product_category: str = '',
                    other_product_images: List[str] = None,
                    ip_images: List[str] = None,
                    reference_text: str = '',
                    reference_images: List[str] = None) -> Dict[str, Any]:
        """
        Submit a new check to the Maidalv API.
        
        Args:
            main_product_image: Base64 encoded image or URL to the main product image
            description: Product description text
            ip_keywords: List of IP keywords to check against
            product_category: Category of the product (optional)
            other_product_images: List of additional product images (optional)
            ip_images: List of IP reference images to check against (optional)
            reference_text: Additional reference text (optional)
            reference_images: List of reference images (optional)
            
        Returns:
            Dict containing response data with check_id, status, and estimated completion time
            
        Raises:
            requests.RequestException: If the API request fails
            ValueError: If required parameters are missing or invalid
        """
        if not main_product_image:
            raise ValueError("main_product_image is required")
        if not description:
            raise ValueError("description is required")
        if not ip_keywords:
            raise ValueError("ip_keywords is required")
            
        payload = {
            "api_key": self.api_key,
            "product_category": product_category,
            "main_product_image": main_product_image,
            "ip_keywords": ip_keywords,
            "description": description,
            "reference_text": reference_text,
        }
        
        # Add optional parameters if provided
        if other_product_images:
            payload["other_product_images"] = other_product_images
        if ip_images:
            payload["ip_images"] = ip_images
        if reference_images:
            payload["reference_images"] = reference_images
            
        response = self.session.post(
            f"{self.base_url}/check_api",
            json=payload
        )
        
        # Raise exception for error status codes
        response.raise_for_status()
        
        return response.json()
    
    def check_status(self, check_id: str) -> Dict[str, Any]:
        """
        Check the status of a previously submitted check.
        
        Args:
            check_id: The check ID returned from submit_check
            
        Returns:
            Dict containing status information:
            - If processing: status, queue_position, estimated_completion_time
            - If completed: status, result (containing full analysis)
            
        Raises:
            requests.RequestException: If the API request fails
            ValueError: If the check_id is invalid
        """
        response = self.session.get(f"{self.base_url}/check_status/{check_id}")
        
        # Raise exception for error status codes
        response.raise_for_status()
        
        return response.json()
    
    def get_results(self, check_id: str, max_retries: int = 60, retry_interval: int = 10) -> Dict[str, Any]:
        """
        Get the results of a check, waiting if necessary until processing is complete.
        
        Args:
            check_id: The check ID returned from submit_check
            max_retries: Maximum number of status check attempts
            retry_interval: Time in seconds between retries
            
        Returns:
            Dict containing the full analysis results
            
        Raises:
            requests.RequestException: If the API request fails
            ValueError: If the check_id is invalid
            TimeoutError: If max_retries is reached without results
        """
        for attempt in range(max_retries):
            status_response = self.check_status(check_id)
            
            if status_response.get('status') == 'completed':
                return status_response
            
            if status_response.get('status') == 'error':
                raise ValueError(f"Error checking status: {status_response.get('message')}")
                
            # Still processing, wait and retry
            print(f"Still processing. Queue position: {status_response.get('queue_position', 'unknown')}, "
                  f"Estimated time: {status_response.get('estimated_completion_time', 'unknown')}")
            
            if attempt < max_retries - 1:  # Don't sleep on the last iteration
                time.sleep(retry_interval)
        
        raise TimeoutError(f"Check {check_id} did not complete within the allowed time")
    
    @staticmethod
    def encode_image_file(image_path: str) -> str:
        """
        Helper method to encode an image file as base64 for API submission.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Base64 encoded image string
        """
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')