import os
import time
import json
import requests
from googleapiclient.discovery import build
import uuid
from selenium.webdriver.common.by import By
from Alerts.Chrome_Driver import get_driver


def search_google(query, num_results=5):
    """Search Google for information about a plaintiff"""
    try:
        service = build("customsearch", "v1", developerKey=os.environ["GOOGLE_API_KEY"])
        result = service.cse().list(q=query, cx=os.environ["GOOGLE_CSE_ID"], num=num_results).execute()
        
        if "items" in result:
            # Combine title and snippet from top 3 results
            context = ""
            for item in result["items"]:
                context += f"{item['title']}: {item.get('snippet', '')}\n"
            return context
        return ""
    except Exception as e:
        print(f"API Google search error: {e}")
        print(f"Trying UC...")
        return search_google_uc(query, num_results)
    
    
def search_google_uc(query, num_results=5):
    driver = get_driver()

    try:
        driver.get(f"https://www.google.com/search?q={query}")

        # Wait for the page to load
        time.sleep(2)

        # Extract search results
        search = driver.find_elements(By.ID, 'search')
        return search[0].text

    except Exception as e:
        print(f"UC Google search error: {e}")
        try: 
            results = driver.find_elements(By.CSS_SELECTOR, 'div.g')
            context = ""
            for result in results[:num_results]:
                    # title = result.find_element(By.TAG_NAME, 'h3').text
                    # snippet = result.find_element(By.CSS_SELECTOR, 'div.VwiC3b').text
                    context += f"{result.text}\n"
            return context
        except Exception as e:
            print(f"UC Google search error: {e}")
            return context

    finally:
        driver.quit()


def search_and_save_images(query, folder_path, num_results=10):
    """Search Google for images and save them to a specified folder"""
    try:
        service = build("customsearch", "v1", developerKey=os.environ["GOOGLE_SEARCH_TRODATA"])
        result = service.cse().list(q=query, cx=os.environ["GOOGLE_SEARCH_TRODATA_PSE"], num=num_results, searchType="image").execute()
        
        if "items" in result:
            if not os.path.exists(folder_path):
                os.makedirs(folder_path)
            
            image_filenames = []  # List to store image filenames
            
            for i, item in enumerate(result["items"]):
                try: 
                    image_url = item['link']
                    image_data = requests.get(image_url).content
                    random_id = uuid.uuid4().hex[:8]  # Generate a random 8-character string
                    image_filename = f"{i}_{random_id}.jpg"  # Use the random string in the filename
                    image_path = os.path.join(folder_path, image_filename)
                    
                    with open(image_path, 'wb') as image_file:
                        image_file.write(image_data)
                    
                    image_filenames.append(image_filename)  # Add filename to list
                except Exception as e:
                    print(f"Error saving image {image_url}: {e}")
            
            print(f"Saved {len(result['items'])} images to {folder_path}")
            return json.dumps(image_filenames)  # Return JSON string of filenames
        else:
            print("No images found.")
            return json.dumps([])  # Return empty JSON array if no images found
    except Exception as e:
        print(f"Google image search error: {e}")
        return json.dumps([])  # Return empty JSON array on error

# def search_and_save_images_rest(query, folder_path, num_results=10):
#     """Search Google for images using the REST API and save them."""
#     api_key = os.environ["GOOGLE_SEARCH_TRODATA"]
#     cse_id = os.environ["GOOGLE_SEARCH_TRODATA_PSE"]
#     url = f"https://www.googleapis.com/customsearch/v1?key={api_key}&cx={cse_id}&q={query}&searchType=image&num={num_results}"

#     try:
#         response = requests.get(url)
#         response.raise_for_status()  # Raise an exception for bad status codes
#         result = response.json()

#         if "items" in result:
#             if not os.path.exists(folder_path):
#                 os.makedirs(folder_path)

#             image_filenames = []
#             for i, item in enumerate(result["items"]):
#                 image_url = item['link']
#                 image_data = requests.get(image_url).content
#                 random_id = uuid.uuid4().hex[:8]
#                 image_filename = f"{i}_{random_id}.jpg"
#                 image_path = os.path.join(folder_path, image_filename)

#                 with open(image_path, 'wb') as image_file:
#                     image_file.write(image_data)

#                 image_filenames.append(image_filename)

#             print(f"Saved {len(result['items'])} images to {folder_path}")
#             return json.dumps(image_filenames)
#         else:
#             print("No images found.")
#             return json.dumps([])
#     except requests.exceptions.RequestException as e:
#         print(f"REST API Google image search error: {e}")
#         return json.dumps([])




if __name__ == "__main__":
    from dotenv import load_dotenv
    load_dotenv()
    search_and_save_images("April Major Rimpo 1955 Essence", "D:/Documents/Programing/TRO/USside/Documents/")
