import os
from qdrant_client import QdrantClient
from qdrant_client.http import models

def delete_points_missing_plaintiff_id():
    # Initialize client using environment variables
    client = QdrantClient(
        url=os.environ.get("QDRANT_URL"),
        api_key=os.environ.get("QDRANT_API_KEY")
    )
    
    collection_name = "IP_Assets"
    batch_size = 100  # Delete in batches to avoid timeouts
    
    try:
        # Search for points missing plaintiff_id field
        next_offset = None
        total_deleted = 0
        
        while True:
            # Scroll through points missing plaintiff_id
            scroll_result = client.scroll(
                collection_name=collection_name,
                scroll_filter=models.Filter(
                    must=[
                        models.IsEmptyCondition(is_empty=models.PayloadField(key="plaintiff_id"))
                    ]
                ),
                limit=batch_size,
                offset=next_offset,
                with_payload=False,
                with_vectors=False
            )
            
            points, next_offset = scroll_result
            
            if not points:
                break  # No more points
                
            point_ids = [point.id for point in points]
            print(f"Deleting {len(point_ids)} points")
            
            # Delete the batch of points
            client.delete(
                collection_name=collection_name,
                points_selector=models.PointIdsList(points=point_ids),
            )
            
            total_deleted += len(point_ids)
        
        print(f"Deletion completed. Total deleted: {total_deleted}")
    
    except Exception as e:
        print(f"Error occurred: {str(e)}")

if __name__ == "__main__":
    delete_points_missing_plaintiff_id()