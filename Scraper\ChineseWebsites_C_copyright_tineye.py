import undetected_chromedriver as uc # You'll manage this outside the function
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
import time
import os
import requests
from PIL import Image
import io
import re # For parsing style attributes
from logdata import log_message
import torch # Added for image similarity
from torchvision import models, transforms # Added for image similarity
from torchvision.models import MobileNet_V2_Weights # Added for image similarity
import numpy as np # Added for image similarity
from sklearn.metrics.pairwise import cosine_similarity # Added for image similarity
import shutil # Added for copying files
from PIL.ExifTags import TAGS # Added for metadata extraction
from Scraper.ChineseWebsites_C_copyright_google import extract_feature_vector, compute_similarity, remove_white_background
from Alerts.Chrome_Driver import get_driver
import traceback
from langfuse import observe
import langfuse


@observe(capture_output=False)
def reverse_search_on_tineye(image_path, output_folder="similar_images", final_folder="final_selection", max_images=10):
    # Initialize variables that will be used throughout the function
    downloaded_image_paths = []
    final_selection_details = []
    similarities = []
    search_results_count = 0

    if not os.path.exists(image_path):
        log_message(f"  ❌ Error: Image path does not exist: {image_path}")
        return []

    driver = get_driver()
    os.makedirs(output_folder, exist_ok=True)
    absolute_image_path = os.path.abspath(image_path)

    try:
        # Extract features from the source image for similarity comparison
        log_message(f"✨ Extracting features from source image: {image_path}")
        source_features = extract_feature_vector(image_path)
        if source_features is None:
            log_message("❌ Error: Could not extract features from source image. Skipping similarity filtering.")
        
        # 1. Go to https://tineye.com/how (or directly to home for upload)
        log_message("Navigating to TinEye...")
        driver.get("https://tineye.com/") # Homepage directly allows upload

        # Wait for the upload input field to be present
        # The input field itself is <input id="upload-box" type="file" ...>
        # It's hidden, so we send keys directly to it.
        upload_input_locator = (By.ID, "upload-box")
        WebDriverWait(driver, 20).until(
            EC.presence_of_element_located(upload_input_locator)
        )
        file_input = driver.find_element(*upload_input_locator)
        
        log_message(f"  Uploading image: {absolute_image_path}")
        file_input.send_keys(absolute_image_path)

        # Wait for search results to appear.
        # Results are shown in divs with data-test="show-compare"
        results_element_locator = (By.CSS_SELECTOR, "div[data-test='show-compare']")
        log_message("  Waiting for search results...")
        WebDriverWait(driver, 20).until( # Increased timeout for upload and search processing
            EC.presence_of_all_elements_located(results_element_locator)
        )
        log_message("  Search results loaded.")
        time.sleep(2) # Allow a bit more time for everything to settle

        # 4. Take picture results, extract link, download, crop.
        image_overlay_links_to_process = []
        
        # Initial find of all clickable thumbnails
        match_elements = driver.find_elements(*results_element_locator)
        results_container_locator = (By.XPATH, "//div[@data-test='show-compare']/..")
        match_containers = driver.find_elements(*results_container_locator)
        log_message(f"Found {len(match_elements)} potential matches on the page.")

        # After finding match elements, store the count
        search_results_count = len(match_elements)

        for i in range(min(len(match_elements), max_images)):
            log_message(f"Processing match {i+1}...")
            
            # Re-fetch elements in each iteration to avoid stale element issues
            # as modals can change the DOM.
            current_match_elements = driver.find_elements(*results_element_locator)
            current_match_containers = driver.find_elements(*results_container_locator)
            if i >= len(current_match_elements) or i >= len(current_match_containers):
                log_message("  Ran out of elements to click, possibly due to page changes or fewer results than expected.")
                break
            
            match_to_click = current_match_elements[i]
            match_containers = current_match_containers[i]

            # First try to extract direct image URL from filename link
            try:
                # Locate the filename link element
                filename_link = match_containers.find_element(By.XPATH, ".//p[contains(@class, 'max-md:hidden')]//span[contains(text(), 'Filename:')]/a")
                image_url = filename_link.get_attribute("href")
                log_message(f"  Found direct image URL: {image_url}")
                
                # Try to download the image directly
                try:
                    s = requests.Session()
                    headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36','Referer': driver.current_url}
                    response = s.get(image_url, headers=headers, timeout=30)
                    response.raise_for_status()

                    img_data = response.content
                    img = Image.open(io.BytesIO(img_data))

                    # Create filename
                    try:
                        url_part = image_url.split('/')[-1].split('?')[0]
                        sanitized_url_part = "".join(c if c.isalnum() else "_" for c in url_part[:30])
                    except:
                        sanitized_url_part = "image"
                    
                    filename = f"tineye_direct_{i+1}_{sanitized_url_part}.jpg"
                    save_path = os.path.join(output_folder, filename)
                    
                    if img.mode in ('RGBA', 'P', 'LA'):
                        img = img.convert('RGB')
                    
                    img.save(save_path)
                    downloaded_image_paths.append(save_path)
                    log_message(f"  ✅ Directly downloaded image to {save_path}")
                    continue  # Skip overlay processing if direct download succeeded
                except Exception as e:
                    log_message(f"  ❌ Direct download failed: {e}. Falling back to overlay method")
            except Exception as e:
                log_message(f"  Could not find filename link: {e}. Using overlay method")

            # If direct download failed or link not found, use the overlay method
            try:
                # Scroll the element into view and click
                driver.execute_script("arguments[0].scrollIntoView(true);", match_to_click)
                time.sleep(0.5) # Brief pause for scrolling
                WebDriverWait(driver, 10).until(EC.element_to_be_clickable(match_to_click)).click()

                # Wait for the overlay/modal to appear
                # The image in the overlay is in a div with data-test="image-toggle" and a background-image style
                # Or there's an <img src="/api/v1/overlay/..." style="display: none;">
                overlay_image_div_locator = (By.CSS_SELECTOR, "div[data-test='image-toggle'][style*='background-image']")
                overlay_image_div = WebDriverWait(driver, 10).until(
                    EC.visibility_of_element_located(overlay_image_div_locator)
                )
                
                style_attribute = overlay_image_div.get_attribute("style")
                url_match = re.search(r'url\("([^"]+)"\)', style_attribute)
                
                if url_match:
                    partial_url = url_match.group(1)
                    if partial_url.startswith("/api/"): # Ensure it's the type of URL we expect
                        full_image_url = f"https://tineye.com{partial_url}"
                        image_overlay_links_to_process.append(full_image_url)
                        log_message(f"  Extracted overlay image URL: {full_image_url}")
                    else:
                        log_message(f"  Skipping non-API URL from style: {partial_url}")
                else:
                    log_message("  Could not extract image URL from overlay style.")

                # Close the overlay
                # The modal container: <div class="bg-[#f7f7f7] absolute ...">
                # The close button image: <img alt="Close compare modal">
                modal_container_selector = "div.bg-\\[\\#f7f7f7\\].absolute" # CSS needs brackets escaped
                
                # More specific selector for the close button's image within the modal
                close_button_img_locator = (By.CSS_SELECTOR, f"{modal_container_selector} img[alt='Close compare modal']")
                
                close_img_element = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable(close_button_img_locator)
                )
                # Click the parent button of the image for better reliability
                close_button_actual = close_img_element.find_element(By.XPATH, "./ancestor::button[1]")
                close_button_actual.click()

                # Wait for the overlay to disappear
                WebDriverWait(driver, 10).until(
                    EC.invisibility_of_element_located((By.CSS_SELECTOR, modal_container_selector))
                )
                time.sleep(0.5) # Brief pause for page to settle

            except Exception as e:
                log_message(f"  Error processing match {i+1}: {e}")
                # Attempt to close modal if it's stuck, then continue
                try:
                    if driver.find_elements(By.CSS_SELECTOR, modal_container_selector): # Check if modal is still there
                        log_message("  Attempting to close stuck modal...")
                        # Use JavaScript click as a fallback if normal click failed or element is obscured
                        close_img_element_fallback = driver.find_element(By.CSS_SELECTOR, f"{modal_container_selector} img[alt='Close compare modal']")
                        close_button_fallback = close_img_element_fallback.find_element(By.XPATH, "./ancestor::button[1]")
                        driver.execute_script("arguments[0].click();", close_button_fallback)
                        WebDriverWait(driver, 5).until(
                            EC.invisibility_of_element_located((By.CSS_SELECTOR, modal_container_selector))
                        )
                except Exception as e_close:
                    log_message(f"  Could not close modal after error: {e_close}. Refreshing page to recover might be needed for robust batch processing.")
                continue # Move to the next image result

        
        # Download and process collected image links (from overlay method)
        for idx, img_url in enumerate(image_overlay_links_to_process):
            if len(downloaded_image_paths) >= max_images:
                break
            try:
                log_message(f"Downloading overlay image {idx+1}/{len(image_overlay_links_to_process)}: {img_url}")
                # Use a session for potential cookie handling, though not strictly needed here
                s = requests.Session()
                # Mimic browser headers slightly
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Referer': driver.current_url # Current page is the search results page
                }
                response = s.get(img_url, headers=headers, timeout=30)
                response.raise_for_status()

                img_data = response.content
                img = Image.open(io.BytesIO(img_data))

                # The image is a composite. Left half is original, right half is match.
                width, height = img.size
                # Crop area: (left, upper, right, lower)
                # We want the right half: from middle-width to full-width, full-height
                crop_area = (width // 2, 0, width, height)
                cropped_img = img.crop(crop_area)

                # Create a somewhat unique filename
                # Extract a part of the URL, sanitize it, and add index
                try:
                    url_part = img_url.split('/')[-1].split('?')[0] # Get the last path segment before query params
                    sanitized_url_part = "".join(c if c.isalnum() else "_" for c in url_part[:30]) # Sanitize and shorten
                except:
                    sanitized_url_part = "image"
                
                filename = f"tineye_match_overlay_{idx+1}_{sanitized_url_part}.jpg" # Save as PNG to preserve quality
                
                save_path = os.path.join(output_folder, filename)
                
                # Convert to RGB if it's RGBA or P (palette) to ensure compatibility with PNG saving
                if cropped_img.mode in ('RGBA', 'P', 'LA'):
                    cropped_img = cropped_img.convert('RGB')
                
                # Save to the main output folder
                cropped_img.save(save_path)
                remove_white_background(save_path) # TinEye often add extra white space to make the result overlap better with the submitted picture
                downloaded_image_paths.append(save_path)
                log_message(f"  ✅ Saved cropped image to {save_path}")

            except requests.exceptions.RequestException as e:
                log_message(f"  ❌ Error downloading {img_url}: {e}")
            except IOError as e: # PIL errors
                log_message(f"  ❌ Error processing image from {img_url}: {e}")
            except Exception as e:
                log_message(f"  ❌ An unexpected error occurred for {img_url}: {e}")

        # --- Similarity Filtering and Final Selection ---
        if downloaded_image_paths and source_features is not None:
            log_message("\n📊 Analyzing image similarity using MobileNetV2...")
            similarities = []

            for img_path in downloaded_image_paths:
                try:
                    features = extract_feature_vector(img_path)
                    if features is not None:
                        similarity = compute_similarity(source_features, features)
                        similarities.append((img_path, similarity))
                        log_message(f"    🖼️ Image: {os.path.basename(img_path)}, Similarity: {similarity:.4f}")
                except Exception as e:
                    log_message(f"  ❌ Error processing similarity for {os.path.basename(img_path)}: {e}")

            if not similarities:
                log_message("  ℹ️ Could not compute similarity for any downloaded images.")
            else:
                # Sort by similarity (highest first)
                similarities.sort(key=lambda x: x[1], reverse=True)
                img_filename = os.path.basename(similarities[0][0])
                dest_path = os.path.join(final_folder, img_filename)
                shutil.copy2(similarities[0][0], dest_path)
                final_selection_details.append({"path": dest_path, "similarity": float(similarity)})
                log_message(f"  ✅ Copied to final selection: {img_filename} (similarity: {similarities[0][1]:.4f})")
        else:
            log_message("  ℹ️ No images downloaded or source features missing. Skipping similarity filtering.")
                
    except Exception as e:
        if "0 matches" in driver.page_source:
            log_message("  ℹ️ No matches found on TinEye. Skipping further processing.")
        elif "Sorry to interrupt" in driver.page_source:
            log_message("  ⚠️ TinEye: Captcha or rate limit detected. Skipping further processing.")
            langfuse.get_client().update_current_span(
                metadata={
                    "Status": "Captcha detected"
                }
            )
        else:
            log_message(f"  ❌ TinEye: An error occurred (different from 0 match): {e}, {traceback.format_exc()}")
            driver.save_screenshot("tineye_error_screenshot.png")
    finally:
        log_message(f"Finished processing. Downloaded {len(downloaded_image_paths)} images and selected {len(final_selection_details)} images for final selection.")
        driver.quit()
        
        # Update observation with safely initialized values
        langfuse.get_client().update_current_span(
            output={
                "SearchResultsNb": search_results_count,
                "DownloadedImagesWithSimilarity": similarities if similarities else [],
                "FinalSelectionDetails": final_selection_details
            }
        )
    
    return final_selection_details

if __name__ == '__main__':
    # --- THIS IS EXAMPLE USAGE ---
    # You need to set up your undetected_chromedriver instance before calling the function.
    # image_to_search = "/mnt/d/Documents/Programing/TRO/ModelTestsWorkbenchData/pictures/copyright/ip/IN_DC_1_23-cv-14825_2023-10-12_1_ 0_Exhibit_1_page27_0.webp"
    image_to_search = "D:\\Documents/Programing/TRO/ModelTestsWorkbenchData/pictures/copyright/ip/IN_DC_1_23-cv-14825_2023-10-12_1_ 0_Exhibit_1_page27_0.webp"
    driver = get_driver()
    output_folder = os.path.join(os.getcwd(), "Documents", "IP", "tineye_downloads")
    os.makedirs(output_folder, exist_ok=True)


    if driver:
        try:
            if os.path.exists(image_to_search):
                downloaded_images = reverse_search_on_tineye(driver, image_to_search, 
                                                                output_folder=output_folder, 
                                                                max_images=10) # Test with 5 images
                log_message("\nDownloaded and cropped images:")
                for p in downloaded_images:
                    log_message(p)
            else:
                log_message(f"Test image '{image_to_search}' not found. Skipping example run.")

        finally:
            log_message("Closing browser...")
            driver.quit()
    else:
        log_message("Driver not initialized. Skipping example run.")