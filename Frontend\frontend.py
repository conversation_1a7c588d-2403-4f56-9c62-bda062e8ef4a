import requests
import json

def refresh_frontend():
    try:
        refresh_url = "https://www.trodata.cn/app-api/alerts/refresh"
        response = requests.post(refresh_url)
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 0:
                print("Cache refreshed successfully")
            else:
                print(f"Cache refresh failed with error message: {result.get('msg')}")
        else:
            print(f"Cache refresh failed with status code: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {str(e)}")


def refresh_steps_frontend():
    try:
        refresh_url = "https://www.trodata.cn/app-api/alerts/progress"
        response = requests.post(refresh_url)
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 0:
                print("Cache refreshed successfully")
            else:
                print(f"Cache refresh failed with error message: {result.get('msg')}")
        else:
            print(f"Cache refresh failed with status code: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    # refresh_frontend()
    refresh_steps_frontend()