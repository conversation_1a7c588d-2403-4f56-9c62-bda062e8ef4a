import os
import json
import aiohttp
import asyncio
from google.oauth2 import service_account
from google.auth.transport.requests import Request
import time
import jwt

# Test endpoints
PROXY_ENDPOINT = 'https://proxyjs.sergedc.workers.dev'
GENAI_ENDPOINT = 'https://genaijs.sergedc.workers.dev'
GENAI_PYTHON_ENDPOINT = 'https://genaipython.sergedc.workers.dev'

# Test configuration
TEST_PROJECT_ID = "trodata"
KEY_FILE_PATH = os.path.join(".", "AI", "GoogleCouldServiceAccountKey", f"{TEST_PROJECT_ID}-key.json")
TEST_PROMPT = "Tell me a short joke about programming."
# Updated model names based on the latest API versions
DEFAULT_GEMINI_MODEL = "gemini-2.0-flash-exp"  # Updated from gemini-1.0-pro
DEFAULT_VERTEX_MODEL = "gemini-2.0-flash-exp"  # For Vertex AI

async def test_proxy(session):
    """Test proxy functionality by accessing Google APIs through the proxy"""
    print(f"\nTesting proxy endpoint: {PROXY_ENDPOINT}")
    
    try:
        # Test accessing oauth2.googleapis.com with proper grant type
        oauth_url = f"{PROXY_ENDPOINT}/https://oauth2.googleapis.com/token"
        print(f"Testing proxy with URL: {oauth_url}")
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        
        # Using a more appropriate grant type with minimal data
        # This will still fail but with a different error that shows the proxy works
        data = {"grant_type": "refresh_token", "refresh_token": "dummy_token"}
        async with session.post(oauth_url, headers=headers, data=data) as response:
            print(f"Proxy OAuth test status: {response.status}")
            response_text = await response.text()
            print(f"Response body: {response_text}...")
            
        # Test accessing generativelanguage.googleapis.com with a real API key
        api_key = None
        for key_env in ["GEMINI_API_KEY_JG", "GEMINI_API_KEY_SDC"]:
            api_key = os.environ.get(key_env, '')
            if api_key:
                print(f"Using API key for proxy test: {key_env}")
                break
                
        genai_url = f"{PROXY_ENDPOINT}/https://generativelanguage.googleapis.com/v1/models"
        print(f"Testing proxy with URL: {genai_url}")
        
        # Using the actual API key instead of dummy
        params = {"key": api_key or "dummy_key_for_testing_format"}
        async with session.get(genai_url, params=params) as response:
            print(f"Proxy Generative AI test status: {response.status}")
            response_text = await response.text()
            print(f"Response body: {response_text}...")
            
    except Exception as e:
        print(f"Error testing proxy: {str(e)}")

async def test_gemini_generation(session):
    """Test Gemini API generation functionality"""
    endpoint = f"{GENAI_ENDPOINT}/gemini_generate"
    print(f"\nTesting Gemini generation endpoint: {endpoint}")
    
    try:
        # Get API key
        api_key = None
        for key_env in ["GEMINI_API_KEY_JG", "GEMINI_API_KEY_SDC"]:
            api_key = os.environ.get(key_env, '')
            if api_key:
                print(f"Using API key: {key_env}")
                break
                
        if not api_key:
            print("Warning: No Gemini API key found in environment variables")
            return
            
        # Prepare request data in proper format for Gemini API
        # Updated to use the proper model and format
        request_data = {
            'model_name': DEFAULT_GEMINI_MODEL,
            'contents': [{"role": "user", "parts": [{"text": TEST_PROMPT}]}],
            'config': {
                'temperature': 0.7
            },
            'api_key': api_key
        }
        
        print(f"Request data: {json.dumps(request_data, indent=2)}")
        
        # Make request to API relay
        async with session.post(endpoint, json=request_data) as response:
            print(f"Gemini API generation status: {response.status}")
            if response.status == 200:
                result = await response.json()
                print(f"Gemini API response: {json.dumps(result, indent=2)}")
            else:
                print(f"Gemini API Error response: {await response.text()}")
            
    except Exception as e:
        print(f"Error testing Gemini generation: {str(e)}")

async def test_vertex_generation(session):
    """Test Vertex AI generation functionality"""
    endpoint = f"{GENAI_ENDPOINT}/vertex_generate"
    print(f"\nTesting Vertex AI generation endpoint: {endpoint}")
    
    try:
        # Load credentials
        credentials = service_account.Credentials.from_service_account_file(
            KEY_FILE_PATH,
            scopes=['https://www.googleapis.com/auth/cloud-platform']
        )
        
        # Refresh token if necessary
        if not credentials.valid:
            credentials.refresh(Request())
        
        # Prepare request data in proper format for Vertex AI
        # Corrected the safety settings format for Vertex API - removed safety settings since they're causing issues
        request_data = {
            'model_name': DEFAULT_VERTEX_MODEL,
            'contents': [{"role": "user", "parts": [{"text": TEST_PROMPT}]}],
            'config': {
                'temperature': 0.7,
                'maxOutputTokens': 512,
                'topP': 0.95,
                'topK': 40
                # Removed safetySettings as they cause errors with the current format
            },
            'project_id': TEST_PROJECT_ID,
            'credentials': {
                'token': credentials.token,
                'type': 'service_account'
            }
        }
        
        print(f"Request data: {json.dumps(request_data, indent=2)}")
        
        # Make request to API relay
        async with session.post(endpoint, json=request_data) as response:
            print(f"Vertex AI generation status: {response.status}")
            if response.status == 200:
                result = await response.json()
                print(f"Vertex AI API response: {json.dumps(result, indent=2)}")
            else:
                print(f"Vertex AI Error response: {await response.text()}")
            
    except Exception as e:
        print(f"Error testing Vertex AI generation: {str(e)}")

async def test_token_request(session):
    """Test token request functionality"""
    endpoint = f"{GENAI_ENDPOINT}/token"
    print(f"\nTesting token request endpoint: {endpoint}")
    
    try:
        # Load credentials from key file directly
        with open(KEY_FILE_PATH, 'r') as key_file:
            key_data = json.load(key_file)
        
        # Create JWT assertion manually
        now = int(time.time())
        payload = {
            "iss": key_data["client_email"],
            "scope": "https://www.googleapis.com/auth/cloud-platform",
            "aud": "https://oauth2.googleapis.com/token",
            "exp": now + 3600,
            "iat": now
        }
        
        # Sign with the private key from the service account
        token = jwt.encode(
            payload, 
            key_data["private_key"], 
            algorithm="RS256"
        )
        
        print(f"Generated JWT token for authentication")
        
        request_data = {
            'credentials': {
                'private_key': token
            }
        }
        
        # Make request to API relay
        async with session.post(endpoint, json=request_data) as response:
            print(f"Token request status: {response.status}")
            if response.status == 200:
                result = await response.json()
                print(f"Token request API response: {json.dumps(result, indent=2)}")
            else:
                print(f"Token request Error response: {await response.text()}")
            
    except Exception as e:
        print(f"Error testing token request: {str(e)}")

async def test_python_generation(session):
    """Test Python generation functionality"""
    endpoint = f"{GENAI_PYTHON_ENDPOINT}/python_generate"
    print(f"\nTesting Python generation endpoint: {endpoint}")

    try:
        # Prepare request data
        request_data = {
            "prompt": "Write a Python function to calculate the factorial of a number.",
            "model": "gemini-2.0-flash-exp"
        }
        
        # Make request to API relay
        async with session.post(endpoint, json=request_data) as response:
            print(f"Python API generation status: {response.status}")
            if response.status == 200:
                print(f"Genaipython API response: {await response.text()}")
                # result = await response.json()
                # print(f"Genaipython API response: {json.dumps(result, indent=2)}")
            else:
                print(f"Genaipython Error response: {await response.text()}")
            
    except Exception as e:
        print(f"Genaipython Error testing Python generation: {str(e)}")



async def main():
    """Run all tests"""
    # Use longer timeout for API calls
    timeout = aiohttp.ClientTimeout(total=30)
    async with aiohttp.ClientSession(timeout=timeout) as session:
        # Test proxy (corresponds to full_proxy_js.js)
        print(f"\n=== Testing JS Proxy ===")
        await test_proxy(session)
        
        # Test API relay endpoints (corresponds to google_api_relay_js.js)
        print(f"\n=== Testing JS API Relay Endpoints ===")
        # await test_gemini_generation(session)
        # await test_vertex_generation(session)
        # await test_token_request(session)
        # await test_python_generation(session)

if __name__ == "__main__":
    asyncio.run(main()) 