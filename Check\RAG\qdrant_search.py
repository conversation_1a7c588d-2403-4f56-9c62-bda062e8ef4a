"""
Unified Qdrant-based implementation for IP asset similarity search.
This module provides functions to find similar IP assets using Qdrant vector database.
"""

import os, re, aiohttp
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv
from langfuse import observe

# Import the embedding functions from RAG_Inference
from Check.RAG.RAG_Inference import get_siglip_embeddings

# Load environment variables
load_dotenv(os.path.join(os.getcwd(), "Qdrant", ".env"))

# Get Qdrant API details from environment variables
QDRANT_API_URL = os.getenv("QDRANT_API_URL")
QDRANT_API_KEY = os.getenv("QDRANT_API_KEY")

@observe()
async def find_similar_assets_qdrant(
    query_image_paths: List[str],
    check_id: str,
    client_id: str,
    ip_type: str,
    cases_df=None,
    plaintiff_df=None,
    plaintiff_id: Optional[str] = None,
    top_n: int = 1,
    similarity_threshold: float = 0.8,
    similarity_threshold_text: float = 0.25
) -> List[Dict[str, Any]]:
    """
    Find similar IP assets using Qdrant vector database.
    
    Args:
        query_image_paths (List[str]): Paths to the query images
        check_id (str): The identifier for this specific check/batch
        client_id (str): The identifier for the client submitting the batch
        ip_type (str): The type of IP assets to search for ("Copyright", "Patent", "Trademark")
        plaintiff_df: DataFrame containing plaintiff information
        plaintiff_id (str, optional): Optional plaintiff ID to filter results
        top_n (int): Number of top similar images to return
        similarity_threshold (float): Minimum cosine similarity score to consider a match for images
        similarity_threshold_text (float): Minimum cosine similarity score to consider a match for text
        
    Returns:
        List[Dict[str, Any]]: List of match information for the top matches
    """
    if len(query_image_paths) == 0:
        return []

    # Generate embeddings for query images using SigLIP
    siglip_embeddings = get_siglip_embeddings(query_image_paths, "image")

    # Prepare the request payload for the forward_check endpoint
    products = []
    for i, path in enumerate(query_image_paths):
        products.append({
            "siglip_vector": siglip_embeddings[i].tolist(),
            "filename": os.path.basename(path)
        })
    
    payload = {
        "client_id": client_id,
        "check_id": check_id,
        "products": products,
        "search_ip_type": ip_type,
        "threshold": similarity_threshold*2-1,
        "threshold_text": similarity_threshold_text,
        "plaintiff_id": plaintiff_id,
        "top_n": top_n
    }
    
    # Make the API request to the forward_check endpoint
    async with aiohttp.ClientSession() as session:
        async with session.post(
            f"{QDRANT_API_URL}/forward_check",
            json=payload,
            headers={
                "Authorization": f"Bearer {QDRANT_API_KEY}",
                "Content-Type": "application/json"
            }
        ) as response:
            if response.status != 200:
                print(f"Error from Qdrant API: {response.status}")
                return []
            
            result = await response.json()
    
    # Process the results
    all_matches = []
    
    for infringement in result.get("results", []):
        product_filename = infringement.get("query_filename")
        query_image_path = next((path for path in query_image_paths if os.path.basename(path) == product_filename), None)
        
        if not query_image_path:
            continue
        
        if infringement.get("ip_type") != ip_type:
            continue
        
        qdrant_metadata = infringement.get("qdrant_metadata", {})
        db_metadata = infringement.get("db_metadata", {})
        
        if qdrant_metadata and "plaintiff_id" in qdrant_metadata: # TRO infringement
            result_plaintiff_id = qdrant_metadata.get("plaintiff_id", [])
            plaintiff_name = plaintiff_df.loc[plaintiff_df['id'] == result_plaintiff_id, 'plaintiff_name'].iloc[0] if not plaintiff_df.loc[plaintiff_df['id'] == result_plaintiff_id].empty else ""
            
            match_info = {
                "query_image_path": query_image_path,
                "similarity": str(infringement.get("score", 0)/2+0.5),
                'plaintiff_id': result_plaintiff_id,
                "ip_type": infringement.get("ip_type", ""),
                "ip_owner": plaintiff_name,
                "docket": qdrant_metadata.get("docket", ""),
                "number_of_cases": qdrant_metadata.get("number_of_cases", 0),
                "reg_no": qdrant_metadata.get("reg_no", ""), # Copyright & Trademark
                "int_cls_list": qdrant_metadata.get("int_cls", []),  # Trademark
                "trademark_text": qdrant_metadata.get("trademark_text", ""),  # Trademark  !!!! This is not in NPY and not in Qdrant !!!! It is in database, field mark_text
                "patent_number": qdrant_metadata.get("patent_number", ""),  # Patent
                "text": qdrant_metadata.get("text", ""),  # Patent
                "data_type": infringement.get("data_type", "") # Image or Text, for Patent: not used
            }
            
            if infringement.get("ip_type") == "Copyright":
                match_info["ip_image"] = [str(qdrant_metadata.get("filename", "")), str(qdrant_metadata.get("full_filename", ""))]
            elif infringement.get("ip_type") == "Trademark":
                match_info["ip_image"] = [str(qdrant_metadata.get("filename", "")), str(qdrant_metadata.get("full_filename", ""))]
            elif infringement.get("ip_type") == "Patent":
                if cases_df is None:
                    raise ValueError("cases_df is required for patent search")
                match_info["ip_image"] = get_patent_pages(cases_df, result_plaintiff_id, qdrant_metadata.get("filename", ""), qdrant_metadata.get("full_filename", ""), qdrant_metadata.get("docket", ""))
            
            # Add specific fields based on IP type
            
            all_matches.append(match_info)
            
        else: # Non-TRO infringement
            match_info = {
                "query_image_path": query_image_path,
                "similarity": str(infringement.get("score", 0)/2+0.5),
                'plaintiff_id': db_metadata.get("plaintiff_id", None),
                "ip_type": infringement.get("ip_type", ""),
                "ip_owner": db_metadata.get("applicant_name", ""),
                "docket": cases_df[cases_df['plaintiff_id'] == db_metadata.get("plaintiff_id", None)].sort_values(by='date_filed', ascending=False)['docket'].iloc[0],
                "number_of_cases": len(cases_df[cases_df['plaintiff_id'] == db_metadata.get("plaintiff_id", None)]),
                "reg_no": db_metadata.get("reg_no", ""), # Copyright & Trademark
                "int_cls_list": db_metadata.get("int_cls", []),  # Trademark
                "trademark_text": db_metadata.get("mark_text", ""),  # Trademark  !!!! This is not in NPY and not in Qdrant !!!! It is in database, field mark_text
                "patent_number": db_metadata.get("document_id", ""),  # Patent
                "text": db_metadata.get("patent_title", ""),  # Patent
                "data_type": infringement.get("data_type", "") # Image or Text, for Patent: not used
            }
            all_matches.append(match_info)
    
    # Return top N results
    return all_matches


def get_patent_pages(cases_df, plaintiff_id, filename, full_filename, docket):
    # Helper function to extract page number robustly using regex
    def extract_page_number(filename_str):
        match = re.search(r'page(\d+)', filename_str)
        if match:
            return int(match.group(1))
        elif "_full" in filename_str: # the certificate comes first
            return 0
        
        # Fallback if page number is not found. This might indicate an unexpected filename format.
        # Depending on requirements, you could raise an error or return a default (e.g., 0 or float('inf')).
        print(f"Warning: Could not extract page number from '{filename_str}'. Defaulting to 0 for sorting.")
        return 99

    if isinstance(filename,str):
        row = cases_df[(cases_df['docket'] == docket) & (cases_df['plaintiff_id'] == plaintiff_id)].iloc[0]
        all_pages = [key for key in row['images']['patents'].keys() if row['images']['patents'][key]['full_filename'][0] == full_filename]
        full_patent = all_pages + [full_filename]
        # Sort by page number
        full_patent = sorted(full_patent, key=extract_page_number)
    else:
        full_patent = filename + [full_filename]
        # Sort by page number
        full_patent = sorted(full_patent, key=extract_page_number)
    return full_patent
