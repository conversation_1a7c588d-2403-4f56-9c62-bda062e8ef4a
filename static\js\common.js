// =============================================
// General Utility Functions
// =============================================

/**
 * Activates a menu item by adding the 'active' class.
 * @param {string} menuId The ID of the menu item element.
 */
function activateMenuItem(menuId) {
    document.addEventListener('DOMContentLoaded', () => {
        const menuElement = document.getElementById(menuId);
        if (menuElement) {
            // Remove 'active' from other main menu items first
            document.querySelectorAll('.main-menu .menu-item.active').forEach(item => {
                item.classList.remove('active');
            });
            // Add 'active' to the target item
            menuElement.classList.add('active');
        }
    });
}

/**
 * Escapes HTML special characters in a string.
 * @param {string} unsafe The string to escape.
 * @returns {string} The escaped string.
 */
function escapeHtml(unsafe) {
    if (typeof unsafe !== 'string') return unsafe;
    return unsafe
         .replace(/&/g, "&") // Must be first
         .replace(/</g, "<")
         .replace(/>/g, ">")
         .replace(/"/g, "\"") // Escape the double quote
         .replace(/'/g, "&#039;");
}

/**
 * Shows or hides a loading indicator within a specified container.
 * Assumes a standard '.loading' element exists within the container.
 * @param {string|HTMLElement} containerOrSelector The container element or its selector.
 * @param {boolean} show True to show loading, false to hide.
 * @param {string} [message='Loading...'] Optional message to display.
 */
function showLoadingState(containerOrSelector, show, message = 'Loading...') {
    const container = typeof containerOrSelector === 'string'
        ? document.querySelector(containerOrSelector)
        : containerOrSelector;

    if (!container) {
        console.warn('showLoadingState: Container not found:', containerOrSelector);
        return;
    }

    let loadingElement = container.querySelector('.loading');
    if (!loadingElement) {
        // Create if it doesn't exist
        loadingElement = document.createElement('div');
        loadingElement.className = 'loading';
        // Try to insert it appropriately, e.g., at the beginning
        container.insertBefore(loadingElement, container.firstChild);
    }

    if (show) {
        loadingElement.textContent = message;
        loadingElement.style.display = 'block'; // Or 'flex', etc., depending on CSS
    } else {
        loadingElement.style.display = 'none';
    }
}

/**
 * Shows an error message within a specified container.
 * Assumes a standard '.error-message' element exists or can be created.
 * @param {string|HTMLElement} containerOrSelector The container element or its selector.
 * * @param {string} message The error message to display.
 */
function showErrorState(containerOrSelector, message) {
     const container = typeof containerOrSelector === 'string'
        ? document.querySelector(containerOrSelector)
        : containerOrSelector;

    if (!container) {
        console.warn('showErrorState: Container not found:', containerOrSelector);
        return;
    }

    // Clear previous content or hide loading/no-results
    container.innerHTML = ''; // Simple clearing, adjust if needed

    const errorElement = document.createElement('div');
    errorElement.className = 'error-message'; // Use common CSS class
    errorElement.innerHTML = `<strong>Error:</strong><p>${escapeHtml(message)}</p>`; // Basic structure
    container.appendChild(errorElement);
    container.style.display = 'block'; // Ensure container is visible
}

/**
 * Shows a "no results" message within a specified container.
 * Assumes a standard '.no-results' element exists or can be created.
 * @param {string|HTMLElement} containerOrSelector The container element or its selector.
 * @param {string} [message='No results found.'] Optional message.
 */
function showNoResultsState(containerOrSelector, message = 'No results found.') {
    const container = typeof containerOrSelector === 'string'
        ? document.querySelector(containerOrSelector)
        : containerOrSelector;

    if (!container) {
        console.warn('showNoResultsState: Container not found:', containerOrSelector);
        return;
    }

     // Clear previous content or hide loading/error
    container.innerHTML = ''; // Simple clearing, adjust if needed

    const noResultsElement = document.createElement('div');
    noResultsElement.className = 'no-results'; // Use common CSS class
    noResultsElement.textContent = message;
    container.appendChild(noResultsElement);
    container.style.display = 'block'; // Ensure container is visible
}


/**
 * Creates a simple full-screen image preview overlay.
 * @param {string} imageUrl The URL of the image to preview.
 */
function previewImage(imageUrl) {
    // Remove existing overlay if any
    const existingOverlay = document.getElementById('image-preview-overlay'); // Use ID from common.css
    if (existingOverlay) {
        document.body.removeChild(existingOverlay);
    }

    // Create overlay element using CSS class
    const overlay = document.createElement('div');
    overlay.id = 'image-preview-overlay'; // Use ID from common.css
    // Classes will be added later to show it

    // Create content container
    const content = document.createElement('div');
    content.id = 'image-preview-content'; // Use ID from common.css

    // Create image element
    const img = document.createElement('img');
    img.id = 'image-preview-img'; // Use ID from common.css
    img.src = imageUrl;
    img.alt = 'Image Preview';

    // Create close button
    const closeBtn = document.createElement('button');
    closeBtn.id = 'image-preview-close'; // Use ID from common.css
    closeBtn.innerHTML = '&times;'; // Simple 'x'

    // Assemble elements
    content.appendChild(img);
    content.appendChild(closeBtn);
    overlay.appendChild(content);

    // Function to close the overlay
    const closeOverlay = () => {
        overlay.classList.remove('show');
        // Optional: Delay removal to allow for fade-out animation if added in CSS
        setTimeout(() => {
             if (document.body.contains(overlay)) {
                 document.body.removeChild(overlay);
             }
        }, 200); // Adjust timing based on potential CSS transition
    };

    // Event listeners
    overlay.addEventListener('click', (event) => {
        // Close only if clicking the background overlay itself, not the content/image
        if (event.target === overlay) {
            closeOverlay();
        }
    });
    closeBtn.addEventListener('click', closeOverlay);

    // Append to body and show
    document.body.appendChild(overlay);
    // Use setTimeout to allow the element to be added to the DOM before adding the class for transition
    setTimeout(() => {
        overlay.classList.add('show');
    }, 10);
}


// =============================================
// Task Runner / Log Streaming Logic (Original common.js)
// =============================================
document.addEventListener('DOMContentLoaded', function () {
    const startTaskButton = document.getElementById('startTask');
    const startUpdateTaskButton = document.getElementById('startUpdateTask');
    const progressBar = document.getElementById('progressBar'); // Assumes this ID exists on pages using this
    const logsDiv = document.getElementById('logs'); // Assumes this ID exists
    const logsContainer = document.getElementById('logsContainer'); // Assumes this ID exists
    const refreshApiKeysButton = document.getElementById('refreshApiKeys');
    let eventSource = null;
    let currentStepName = '';
    let lastRunningStepName = '';
    let lastDisplayedLogMessage = null;
    let stepNames = []; // Initialize stepNames array

    // --- OCR Progress Bar Specific ---
    let ocrTotalPages = 0;
    let ocrCurrentPage = 0;
    const OCR_PROGRESS_BAR_ID = 'ocr-progress-bar'; // Unique ID for OCR progress

    function updateOcrProgress() {
        const progressBarElement = document.getElementById(OCR_PROGRESS_BAR_ID);
        if (progressBarElement && ocrTotalPages > 0) {
            const percent = Math.min(100, Math.round((ocrCurrentPage / ocrTotalPages) * 100));
            const progressBarInner = progressBarElement.querySelector('.progress-bar');
            if (progressBarInner) {
                progressBarInner.style.width = `${percent}%`;
                progressBarInner.textContent = `${percent}%`;
                progressBarInner.setAttribute('aria-valuenow', percent);
            }
        }
    }

    function showOcrProgress(totalPages) {
        removeOcrProgress(); // Remove any existing one first
        if (totalPages <= 0 || !logsContainer) return;

        ocrTotalPages = totalPages;
        ocrCurrentPage = 0;

        const progressBarContainer = document.createElement('div');
        progressBarContainer.id = OCR_PROGRESS_BAR_ID;
        progressBarContainer.className = 'progress-container'; // Optional class for styling
        progressBarContainer.style.marginTop = '10px'; // Add some space

        progressBarContainer.innerHTML = `
            <div class="progress">
                <div class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="${ocrTotalPages}">0%</div>
            </div>
            <small>OCR Progress (Page ${ocrCurrentPage}/${ocrTotalPages})</small>
        `;
        // Insert after the logs container
        logsContainer.parentNode.insertBefore(progressBarContainer, logsContainer.nextSibling);
        updateOcrProgress(); // Initialize display
    }

    function removeOcrProgress(message = '') {
        const existingProgressBar = document.getElementById(OCR_PROGRESS_BAR_ID);
        if (existingProgressBar) {
            console.log('OCR Progress bar removed. Triggering message:', message);
            existingProgressBar.remove();
        }
        ocrTotalPages = 0;
        ocrCurrentPage = 0;
    }
    // --- End OCR Progress Bar Specific ---


    if (startTaskButton) {
        startTaskButton.addEventListener('click', function () {
            if (logsDiv) logsDiv.innerHTML = ''; // Clear logs
            removeOcrProgress(); // Clear OCR progress
            resetProgressBarSteps(); // Reset visual steps

            let selectedDate = document.getElementById('taskDate')?.value;
            if (!selectedDate) {
                selectedDate = new Date().toISOString().split('T')[0];
            }
            const loopBackDays = document.getElementById('loopBackDays')?.value;
            const forceRedo = document.getElementById('forceRedo')?.checked;

            fetch('/start_task', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ selectedDate, loopBackDays, forceRedo })
            })
            .then(response => response.json())
            .then(data => {
                if (data.run_id) monitorRun(data.run_id);
            })
            .catch(error => console.error('Error starting task:', error));
        });
    }

    if (startUpdateTaskButton) {
        startUpdateTaskButton.addEventListener('click', function () {
            if (logsDiv) logsDiv.innerHTML = ''; // Clear logs
            removeOcrProgress(); // Clear OCR progress
            resetProgressBarSteps(); // Reset visual steps

            const loopBackDays = document.getElementById('loopBackDays')?.value;

            fetch('/start_update_task', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ loopBackDays })
            })
            .then(response => response.json())
            .then(data => {
                if (data.run_id) monitorRun(data.run_id);
            })
            .catch(error => console.error('Error starting update task:', error));
        });
    }

    if (refreshApiKeysButton) {
        refreshApiKeysButton.addEventListener('click', function() {
            fetch('/refresh_api_keys', { method: 'POST', credentials: 'same-origin' })
            .then(response => {
                if (!response.ok) {
                    if(response.status === 401) window.location.href = '/login';
                    throw new Error('Refresh failed');
                }
                return response.json();
            })
            .then(data => alert(`API keys refreshed! Loaded ${data.keys_loaded} keys`))
            .catch(error => alert('Error refreshing keys: ' + error.message));
        });
    }

    // Auto-monitor if runId is defined globally (e.g., from template)
    if (typeof runId !== 'undefined' && runId !== null && !startTaskButton && !startUpdateTaskButton) {
        monitorRun(runId);
    }

    function resetProgressBarSteps() {
        if (!progressBar) return;
        const steps = progressBar.querySelectorAll('.step');
        steps.forEach(step => {
            step.classList.remove('pending', 'running', 'completed', 'failed');
            step.classList.add('pending');
        });
    }

    function monitorRun(runIdToMonitor) {
        if (eventSource) eventSource.close(); // Close previous connection if any

        currentStepName = '';
        lastRunningStepName = '';
        stepNames = []; // Reset step names for the new run
        lastDisplayedLogMessage = null;
        removeOcrProgress(); // Ensure OCR progress is cleared

        eventSource = new EventSource('/stream/' + runIdToMonitor);

        eventSource.onmessage = function (e) {
            try {
                const data = JSON.parse(e.data);
                if (data.type === 'log') {
                    handleLogMessage(data);
                } else if (data.type === 'status') {
                    handleStatusUpdate(data);
                }
            } catch (error) {
                console.error('Error parsing SSE data:', error, 'Data:', e.data);
            }
        };

        eventSource.onerror = function(e) {
            console.error('EventSource failed:', e);
            eventSource.close();
            // Optionally display an error to the user
        };
    }

    function handleLogMessage(logData) {
        if (!logsDiv || !logsContainer) return; // Ensure elements exist

        // Only process logs for the currently selected step and avoid duplicates
        if (logData.step === currentStepName && logData.message !== lastDisplayedLogMessage) {
            const message = logData.message;
            lastDisplayedLogMessage = message; // Update last message immediately

            // OCR Progress Handling
            const ocrStartMatch = message.match(/\(OCR\).*page count: (\d+)/);
            const ocrPageMatch = message.match(/text_in_page:.*page (\d+) done/);

            if (ocrStartMatch) {
                const total = parseInt(ocrStartMatch[1], 10);
                console.log('Starting OCR process with', total, 'pages');
                showOcrProgress(total);
            } else if (ocrPageMatch && ocrTotalPages > 0) {
                ocrCurrentPage = parseInt(ocrPageMatch[1], 10);
                console.log(`OCR Page completed: ${ocrCurrentPage}/${ocrTotalPages}`);
                updateOcrProgress();
                if (ocrCurrentPage >= ocrTotalPages) {
                    console.log('OCR completed, removing progress bar soon.');
                    // Delay removal slightly to ensure final update is visible
                    setTimeout(() => removeOcrProgress('OCR Complete'), 1000);
                }
            } else {
                // If it's not an OCR progress message, ensure any OCR bar is removed
                // (unless it's the initial OCR start message)
                if (!ocrStartMatch) {
                    removeOcrProgress(message); // Remove if a non-OCR message arrives
                }
                // Add the log message
                const p = document.createElement('p');
                p.textContent = message;
                logsDiv.appendChild(p);
            }

            // Scroll to bottom
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }
    }

    function handleStatusUpdate(statusData) {
        if (!progressBar) return; // Ensure progress bar exists

        const steps = statusData.steps;
        let currentRunningStepName = '';

        // Update stepNames if it's empty (first status update)
        if (stepNames.length === 0 && steps && steps.length > 0) {
            stepNames = steps.map(step => step[0]);
            setupStepClickListeners(); // Set up click listeners now that we have names/indices
        }

        // Update visual status of each step
        steps.forEach((step, index) => {
            const [stepName, stepStatus] = step;
            const stepDiv = progressBar.querySelector(`.step[data-step="${index}"]`);
            if (stepDiv) {
                stepDiv.classList.remove('pending', 'running', 'completed', 'failed');
                stepDiv.classList.add(stepStatus.toLowerCase());
            }
            if (stepStatus.toLowerCase() === 'running') {
                currentRunningStepName = stepName;
            }
        });

        // If the running step changed, fetch its logs
        if (currentRunningStepName && currentRunningStepName !== lastRunningStepName) {
            currentStepName = currentRunningStepName; // Update the globally tracked current step
            console.log("Running step changed to:", currentStepName);
            fetchLogs(runId, stepNames.indexOf(currentStepName)); // Fetch logs for the new running step
            lastRunningStepName = currentRunningStepName;
            removeOcrProgress(); // Remove OCR bar when step changes
        }

        // Close SSE connection if the run is finished
        if (statusData.run_status !== 'Running' && eventSource) {
            console.log("Run finished, closing EventSource.");
            eventSource.close();
            removeOcrProgress(); // Ensure OCR bar is removed on completion/failure
        }
    }

    function setupStepClickListeners() {
        if (!progressBar || stepNames.length === 0) return;

        const steps = progressBar.querySelectorAll('.step');
        steps.forEach((step, index) => {
            // Remove existing listener to prevent duplicates if called multiple times
            step.replaceWith(step.cloneNode(true));
        });

        // Add new listeners
        progressBar.querySelectorAll('.step').forEach((step, index) => {
            step.addEventListener('click', function () {
                if (index < stepNames.length) {
                    currentStepName = stepNames[index]; // Update global current step
                    console.log("Clicked step:", currentStepName);
                    fetchLogs(runId, index); // Fetch logs for the clicked step
                    removeOcrProgress(); // Remove OCR bar when manually changing steps
                }
            });
        });
    }

    function fetchLogs(runIdToFetch, stepIndex) {
        if (!logsDiv || stepIndex < 0) return; // Ensure elements and valid index

        fetch(`/get_logs/${runIdToFetch}/${stepIndex}`)
            .then(response => response.json())
            .then(data => {
                logsDiv.innerHTML = ''; // Clear previous logs for this step
                lastDisplayedLogMessage = null; // Reset last displayed message
                removeOcrProgress(); // Clear any OCR progress bar

                if (data.logs && data.logs.length > 0) {
                    data.logs.forEach(log => {
                        // Basic duplicate check (might need improvement if IDs aren't reliable)
                        if (log.message !== lastDisplayedLogMessage) {
                            const p = document.createElement('p');
                            p.textContent = log.message;
                            logsDiv.appendChild(p);
                            lastDisplayedLogMessage = log.message;
                        }
                    });
                } else {
                    logsDiv.innerHTML = '<p>No logs available for this step.</p>';
                }
                // Scroll to top after loading historical logs
                if (logsContainer) logsContainer.scrollTop = 0;
            })
            .catch(error => {
                console.error('Error fetching logs:', error);
                logsDiv.innerHTML = '<p>Error loading logs.</p>';
            });
    }
});
// =============================================
// UI Helper Functions (Added during debugging)
// =============================================

/**
 * Adds a labeled information item to a container.
 * @param {HTMLElement} container The parent element to append to.
 * @param {string} label The label text for the info item.
 * @param {string} value The value text for the info item.
 */
function addInfoItem(container, label, value) {
    const infoItem = document.createElement('div');
    infoItem.className = 'info-item';

    const labelEl = document.createElement('span');
    labelEl.className = 'info-label';
    // Remove the colon from the label if it already has one
    const labelText = label.endsWith(':') ? label : label + ':';
    labelEl.textContent = labelText;

    const valueElement = document.createElement('span');
    valueElement.className = 'info-value';

    // Check if this is plaintiff and log for debugging
    if (label === 'Plaintiff') {
        console.log(`addInfoItem for Plaintiff with value ${value}, container:`, container);
    }

    // Skip the special plaintiff handling since we now handle it in the title
    valueElement.textContent = value || 'N/A'; // Ensure a fallback

    infoItem.appendChild(labelEl);
    infoItem.appendChild(valueElement);
    container.appendChild(infoItem);
    return infoItem; // Ensure the created item is returned
}

/**
 * Adds a labeled information item specifically for IP details.
 * @param {HTMLElement} container The parent element to append to.
 * @param {string} label The label text for the info item.
 * @param {string} value The value text for the info item.
 */
function addIpInfoItem(container, label, value) {
    const infoItem = document.createElement('div');
    // Use a specific class for IP items if needed, otherwise use 'info-item'
    infoItem.className = 'ip-info-item info-item'; // Add both for potential styling

    const labelEl = document.createElement('span');
    labelEl.className = 'info-label';
    const labelText = label.endsWith(':') ? label : label + ':';
    labelEl.textContent = labelText;

    const valueElement = document.createElement('span');
    valueElement.className = 'info-value'; // Or 'ip-info-value' if specific styling exists
    valueElement.textContent = value || 'N/A'; // Ensure a fallback

    infoItem.appendChild(labelEl);
    infoItem.appendChild(valueElement);
    container.appendChild(infoItem);
    return infoItem; // Ensure the created item is returned
}

/**
 * Constructs the URL for an IP image.
 * @param {number|string} plaintiffId The ID of the plaintiff.
 * @param {string} imageName The filename of the image.
 * @param {string} [ipType=null] The type of IP ('trademarks', 'patents', 'copyrights'). Used to determine image quality.
 * @returns {string} The full URL to the image.
 */
function getImagePath(plaintiffId, imageName, ipType = null) {
    // Use high quality for patents (certificates), low quality for others
    // Note: This logic might need refinement if 'full_filename' images (certificates)
    // should always be 'high' regardless of IP type. The makeInfoItemValueLinkable
    // function below assumes 'high' for linked files.
    const quality = (ipType === 'patents') ? 'high' : 'low';
    // Ensure imageName is not null or undefined before using it
    const validImageName = imageName || '';
    return `http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/${plaintiffId}/${quality}/${validImageName}`;
}

/**
 * Constructs the URL for a certificate/full file image, which is typically high quality.
 * @param {number|string} plaintiffId The ID of the plaintiff.
 * @param {string} filename The filename of the certificate/full file.
 * @returns {string} The full URL to the certificate image.
 */
function getCertificateImageUrl(plaintiffId, filename) {
     // Ensure filename is not null or undefined before using it
    const validFilename = filename || '';
    return `http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/${plaintiffId}/high/${validFilename}`;
}

/**
 * Makes the value(s) within an IP info item linkable to corresponding full filenames.
 * Handles cases where values and filenames are arrays.
 * @param {HTMLElement} infoItemElement The div element created by addIpInfoItem.
 * @param {number|string} plaintiffId The ID of the plaintiff.
 * @param {string} label The label text (e.g., 'Reg #:', 'Patent #:'). Used in alerts.
 * @param {string|string[]} values The value(s) to display and link (e.g., reg number(s), patent number(s)).
 * @param {string[]} fullFilenames The array of corresponding full filenames.
 */
function makeInfoItemValueLinkable(infoItemElement, plaintiffId, label, values, fullFilenames) {
    if (!infoItemElement) {
        console.warn(`makeInfoItemValueLinkable: infoItemElement is missing for label "${label}".`);
        return;
    }

    const valueSpan = infoItemElement.querySelector('span:last-child');
    if (!valueSpan) {
        console.warn(`makeInfoItemValueLinkable: Could not find value span in infoItemElement for label "${label}".`);
        return;
    }

    // Ensure values is an array
    const valuesArray = Array.isArray(values) ? values : (values !== null && values !== undefined && values !== 'N/A' ? [values] : []);
    // Ensure fullFilenames is an array
    const filenamesArray = Array.isArray(fullFilenames) ? fullFilenames : [];

    // Check for valid data and matching lengths
    if (valuesArray.length === 0 || filenamesArray.length === 0 || valuesArray.length !== filenamesArray.length) {
        // If data is missing or lengths don't match, just display the original value(s) as text
        valueSpan.textContent = Array.isArray(values) ? values.join(', ') || 'N/A' : values || 'N/A';
        if (valuesArray.length > 0 && filenamesArray.length > 0 && valuesArray.length !== filenamesArray.length) {
            console.warn(`makeInfoItemValueLinkable: Mismatch between values count (${valuesArray.length}) and filenames count (${filenamesArray.length}) for label "${label}". Cannot create links.`);
        } else if (valuesArray.length > 0 && filenamesArray.length === 0) {
            console.warn(`makeInfoItemValueLinkable: No full filenames provided for label "${label}" with values: ${valuesArray.join(', ')}. Cannot create links.`);
        }
        return;
    }

    // Clear existing content to replace with links
    valueSpan.innerHTML = '';

    valuesArray.forEach((value, index) => {
        if (index > 0) {
            valueSpan.appendChild(document.createTextNode(', ')); // Add separator
        }

        const link = document.createElement('a');
        link.href = 'javascript:void(0);'; // Use void(0) to prevent page navigation
        link.textContent = value || 'N/A'; // If value is empty, display 'N/A' as link text
        link.style.cursor = 'pointer';
        link.style.textDecoration = 'underline';

        const filename = filenamesArray[index];

        link.addEventListener('click', function(e) {
            e.preventDefault(); // Prevent default link behavior
            if (filename) {
                const certUrl = getCertificateImageUrl(plaintiffId, filename);
                previewImage(certUrl); // Call the common preview function
            } else {
                alert(`Image preview not available for ${label.replace(':', '').trim()} "${value}". Full filename is missing.`);
            }
        });

        valueSpan.appendChild(link);
    });
}