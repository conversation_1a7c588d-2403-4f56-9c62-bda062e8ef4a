global:
  scrape_interval: 15s # Default scrape interval
  evaluation_interval: 15s # How often to evaluate rules

scrape_configs:
  # 1. Scrape Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      # Use the service name defined in docker-compose
      - targets: ['prometheus:9090']

  # 2. Scrape Qdrant
  - job_name: 'qdrant'
    metrics_path: /metrics # Qdrant exposes metrics here by default
    scheme: https
    static_configs:
      # Use the service name and default port defined in docker-compose
      - targets: ['qdrant:6333']
    tls_config:
          # This allows Prometheus to connect even if the hostname 'qdrant' doesn't match the certificate's name 'maidalv.com'.
          insecure_skip_verify: true
    authorization:
      type: Bearer
      credentials: 2WFupSDRbBCCqz4L6o31unBJX5fVtIIJ

  # 3. Scrape Node Exporter (Host OS Metrics)
  - job_name: 'node-exporter'
    static_configs:
      # Use the service name and default port (9100)
      - targets: ['node-exporter:9100']

  # 4. Scrape PostgreSQL Exporter
  - job_name: 'postgres-exporter'
    static_configs:
      # Use the service name and default port (9187)
      - targets: ['postgres-exporter:9187']

  # 5. Scrape cAdvisor (Container Metrics)
  - job_name: 'cadvisor'
    static_configs:
      # Use the service name and default port (8080)
      - targets: ['cadvisor:8080']

# Optional: Add alerting rules configuration here if using Prometheus Alertmanager
# alerting:
#   alertmanagers:
#     - static_configs:
#         - targets: ['alertmanager:9093'] # If you add an Alertmanager service
# rule_files:
#   - "alert.rules.yml"