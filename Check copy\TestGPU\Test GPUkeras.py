import tensorflow as tf
from tf_keras import applications
from PIL import Image
from tf_keras import preprocessing
import numpy as np
import time
import os

print(tf.config.list_physical_devices('GPU'))

# Additional check to confirm Tensor<PERSON>low can see the GPU
if tf.config.list_physical_devices('GPU'):

    print("TensorFlow is using the GPU")
else:
    print("TensorFlow is NOT using the GPU")
    print("Check CUDA and cuDNN installation and environment variables.")

# You can also try setting memory growth to allow Tensor<PERSON>low to allocate memory as needed
gpus = tf.config.list_physical_devices('GPU')
if gpus:
    try:
        # Currently, memory growth needs to be the same across GPUs
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        logical_gpus = tf.config.list_logical_devices('GPU')
        print(len(gpus), "Physical GPUs,", len(logical_gpus), "Logical GPUs")
    except RuntimeError as e:
        # Memory growth must be set before GPUs have been initialized
        print(e)


model_efficientnet = applications.EfficientNetB7(weights='imagenet', include_top=False, pooling='avg')

def preprocess_image(image_path, res=480):
    """Preprocess the image to match EfficientNetB7 input requirements."""
    img = Image.open(image_path).convert('RGB')
    # img = img.resize((600, 600))  # Resize to EfficientNetB7 input size
    img = img.resize((res, res))
    x = preprocessing.image.img_to_array(img)
    x = np.expand_dims(x, axis=0)  # Add batch dimension
    x = applications.efficientnet.preprocess_input(x)  # Apply EfficientNet preprocessing
    return x


folder_path = os.path.join(os.getcwd(), "data", "EvidencesJanFeb")
image_paths = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp'))]
image_paths = image_paths[:2]
preprocessed_images = [preprocess_image(image_path, 480) for image_path in image_paths]
x = np.concatenate(preprocessed_images, axis=0)  # Combine into a batch

time_start = time.time()
embeddings = model_efficientnet.predict(x)
time_end = time.time()
print(f"Time taken: {time_end - time_start:.1f} seconds")
