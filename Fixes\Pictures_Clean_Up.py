import zlib
import base64
import json
from DatabaseManagement.ImportExport import get_table_from_GZ, insert_and_update_df_to_GZ_batch
from DatabaseManagement.Connections import get_gz_connection
from Alerts.ReprocessCases import reprocess_cases
import pandas as pd
from FileManagement.Tencent_COS import delete_cos_files, list_all_files_in_COS, get_cos_client
import pickle
import os

def create_full_list_of_pictures(df_cases, force_refresh=False):
    if not force_refresh:
        if os.path.exists("full_list_of_pictures.pkl") and os.path.exists("full_list_of_certificates.pkl"):
            with open("full_list_of_pictures.pkl", "rb") as f:
                full_list_of_pictures = pickle.load(f)
            with open("full_list_of_certificates.pkl", "rb") as f:
                full_list_of_certificates = pickle.load(f)
            return full_list_of_pictures, full_list_of_certificates


    full_list_of_pictures = set()
    full_list_of_certificates = set()
    for index, row in df_cases.iterrows():
        plaintiff_id = row["plaintiff_id"]
        for ip in ["patents", "trademarks", "copyrights"]:
            if ip in row["images"].keys():
                for key, value in row["images"][ip].items():
                    full_list_of_pictures.add((plaintiff_id, key))
                    if row["images"][ip][key]["full_filename"]:
                        for picture in row["images"][ip][key]["full_filename"]:
                            full_list_of_certificates.add((plaintiff_id, picture))

    with open("full_list_of_pictures.pkl", "wb") as f:
        pickle.dump(full_list_of_pictures, f)
    with open("full_list_of_certificates.pkl", "wb") as f:
        pickle.dump(full_list_of_certificates, f)

    return full_list_of_pictures, full_list_of_certificates


def sync_pictures_on_cos_to_df(df_cases, full_list_of_pictures, full_list_of_certificates):
    all_cos_files = list_all_files_in_COS("plaintiff_images/")

    cases_to_reprocess = set()
    for plaintiff_id, picture in full_list_of_pictures:
        key = f'plaintiff_images/{int(plaintiff_id)}/high/{picture}'
        key2 = f'plaintiff_images/{int(plaintiff_id)}/low/{picture}'
        if key not in all_cos_files or key2 not in all_cos_files:
            cases_for_plaintiff_id = df_cases[df_cases["plaintiff_id"] == plaintiff_id]
            case_id = None
            for index, row in cases_for_plaintiff_id.iterrows():
                for ip in ["patents", "trademarks", "copyrights"]:
                    if ip in row["images"].keys():
                        for key, value in row["images"][ip].items():
                            if value["full_filename"] and picture in value["full_filename"]:
                                case_id = row["id"]
                                break
                    if case_id is not None:
                        break
                if case_id is not None:
                    break

            print(f"Picture {picture} not found on COS for plaintiff {plaintiff_id}, case {case_id} => reprocessing case")
            cases_to_reprocess.add(case_id)

    for plaintiff_id, picture in full_list_of_certificates:
        key = f'plaintiff_images/{int(plaintiff_id)}/high/{picture}'
        if key not in all_cos_files:
            cases_for_plaintiff_id = df_cases[df_cases["plaintiff_id"] == plaintiff_id]
            case_id = None
            for index, row in cases_for_plaintiff_id.iterrows():
                for ip in ["patents", "trademarks", "copyrights"]:
                    if ip in row["images"].keys():
                        for key, value in row["images"][ip].items():
                            if value["full_filename"] and picture in value["full_filename"]:
                                case_id = row["id"]
                                break
                    if case_id is not None:
                        break
                if case_id is not None:
                    break

            print(f"Certificate {picture} not found on COS for plaintiff {plaintiff_id}, case {case_id}")
            cases_to_reprocess.add(case_id)

    df_cases_to_reprocess = df_cases[df_cases["id"].isin(cases_to_reprocess)]
    print(f"Cases to reprocess: {len(df_cases_to_reprocess)}")
    # reprocess_case(df_cases, df_cases_to_reprocess, df_plaintiff)

    files_to_delete = set()
    for file in all_cos_files:
        filename = file.split("/")[-1]
        plaintiff_id = file.split("/")[1]
        try:
            if (int(plaintiff_id), filename) not in full_list_of_pictures and (int(plaintiff_id), filename) not in full_list_of_certificates:
                print(f"Plaintiff {plaintiff_id} - Picture {filename} is on the COS but is not required => deleting")
                files_to_delete.add(file)
        except Exception as e:
            print(f"Error adding file to files_to_delete: {file}: {e}")

    client, bucket = get_cos_client()
    # delete_cos_files(client, bucket, files_to_delete)


    print(f"Files to delete: {len(files_to_delete)}")
    print(f"Cases to reprocess: {len(df_cases_to_reprocess)}")





if __name__ == "__main__":
    df_plaintiff = get_table_from_GZ("tb_plaintiff", force_refresh=True)
    # full_list_of_pictures, full_list_of_certificates = create_full_list_of_pictures(df_cases)
    # sync_pictures_on_cos_to_df(df_cases, full_list_of_pictures, full_list_of_certificates)
    # # # sync_pictures_on_nas(df_cases, df_plaintiff, full_list_of_pictures, full_list_of_certificates)

    conn = get_gz_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT id, images FROM tb_case")
    data = cursor.fetchall()
    df_cases = pd.DataFrame(data, columns=[desc[0] for desc in cursor.description])
    corrupted_indices = []
    for i, (index, row) in enumerate(df_cases.iterrows()):
        try:
            images = row['images']
            decoded_images = json.loads(zlib.decompress(base64.b64decode(images)).decode('utf-8'))
        except json.JSONDecodeError:
            corrupted_indices.append(i)
        except Exception as e:
            corrupted_indices.append(i)

    print(f"Corrupted indices ({len(corrupted_indices)}): {corrupted_indices}")
    df = df_cases.iloc[corrupted_indices]
    print(df["docket"])
    # df = df[0:2]

    # Use the centralized reprocessing function
    success = reprocess_cases(cases_to_reprocess=df, trace_name="Pictures Clean Up", full_cases_df=df_cases, plaintiff_df=df_plaintiff)

    if success:
        print(f"Successfully reprocessed {len(df)} cases with corrupted images")
    else:
        print(f"Reprocessing completed with some failures for {len(df)} cases")
