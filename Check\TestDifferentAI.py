import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from DatabaseManagement.ImportExport import get_table_from_GZ
import cv2
from Common.Constants import sanitize_name
import pandas as pd
from AI.LLM_shared import get_json
# from AI.Mistral import pixtral
from AI.Unused.GCV_Logo import Google_detect_logos
from AI.GC_VertexAI import vertex_genai_image, vertex_llama_image_curl, vertex_genai_image_async
from AI.Unused.Groc import groq_image
from Check.Do_Check import check
import tempfile
from FileManagement.Tencent_COS import get_cos_client
import asyncio
from Common.Constants import sem_task


def rename_files():
    df_cases = get_table_from_GZ("tb_case")
    df_cases = df_cases[df_cases["date_filed"].astype(str).str.contains("2024")]
    df_plaintiffs = get_table_from_GZ("tb_plaintiff")

    product_path = "D:/Documents/Programing/TRO/USside/Documents/AssessFiles/For AI/Products"
    for i, file in enumerate(os.listdir(product_path)):
        case_number = file.split("cv")[1].split("_")[0]  # Extract case number from the file name
        while len(case_number) < 5:
            case_number = "0" + case_number
        case_row = df_cases[df_cases["docket"].str.contains(case_number)]
        plaintiff_id = case_row["plaintiff_id"].values[0]
        plaintiff = df_plaintiffs[df_plaintiffs["id"] == plaintiff_id]["plaintiff_name"].values[0]

        os.rename(os.path.join(product_path, file), os.path.join(product_path, f"{i}_A_{sanitize_name(plaintiff)}.jpg"))


def get_logo_google():
    product_path = "D:/Documents/Programing/TRO/USside/Documents/AssessFiles/For AI/3. Products - SmartCrop"
    results_df = pd.DataFrame(columns=["picture", "Plaintiff", "AI_brand", "AI_confidence"])
    for i, file in enumerate(os.listdir(product_path)):
        extracted_logos, logos_descriptions = Google_detect_logos(os.path.join(product_path, file))
        plaintiff = file.split("_")[-1].split(".")[0]

        if len(extracted_logos) == 0:
            results_df.loc[len(results_df)] = [file, plaintiff, "", -1]
            continue
        
        for j, logo in enumerate(extracted_logos):
            results_df.loc[len(results_df)] = [file, plaintiff, logos_descriptions[j][0], logos_descriptions[j][1]]
            cv2.imwrite(f"D:/Documents/Programing/TRO/USside/Documents/AssessFiles/For AI/3. Products - SmartCrop/{file.split('.')[0]}_{j}.jpg", logo)
        
    results_df.to_excel("D:/Documents/Programing/TRO/USside/Documents/AssessFiles/For AI/results.xlsx", index=False)


def get_brand_gemini():
    product_path = "D:/Documents/Programing/TRO/USside/Documents/AssessFiles/For AI/3. Products - SmartCrop"
    # product_path = "D:/Documents/Programing/TRO/USside/Documents/AssessFiles/For AI/ChromeHeart"
    
    results_df = pd.DataFrame(columns=["picture", "Plaintiff", "AI_brand", "AI_confidence"])
    files = os.listdir(product_path)
    # files = files[-85] #Gemini flash status
    # files = files[515:]
    for i, file in enumerate(files):
        plaintiff = file.replace("_cropped", "").split("_")[-1].split(".")[0]
        prompt = 'This is a product being sold on the internet. What is the brand of the product being sold? How confident are you? Return the answer in a json: {"brand":"coca cola", "confidence": 0.87}. If you do not know the brand, return  {"brand":"unknown", "confidence": 0}'
        # prompt = 'This is a product being sold on the internet. What is the brand of the product being sold? How confident are you? Return the answer in a json: {"brand":"coca cola"}. If you cannot identify any brand, return {"brand":"unknown"}'
        # prompt = 'This is a product being sold on the internet. What is the brand of the product being sold? Return the answer in a json: {"brand":"coca cola"}.'
        
        # answer_ai = llm_call_image2(prompt, image_path=os.path.join(product_path, file))
        # print(f"{i}/{len(files)}: {answer_ai['response'].text}")
        # results_df.loc[len(results_df)] = [file, plaintiff, get_json(answer_ai["response"].text)["brand"], get_json(answer_ai["response"].text)["confidence"]]
        
        # answer_ai = pixtral(prompt, os.path.join(product_path, file), model="pixtral-large-latest") #pixtral-12b-2409

        # answer_ai = vertex_gemini_image(prompt, image_path=os.path.join(product_path, file), model_name='gemini-pro-experimental') # , model_name='gemini-1.5-pro-002'  model_name='gemini-flash-experimental'
        # answer_ai = vertex_llama_image_curl(prompt, image_path=os.path.join(product_path, file))
        # answer_ai = groq_image(prompt, image_path=os.path.join(product_path, file))
        answer_ai = vertex_genai_image(prompt, image_path=os.path.join(product_path, file), model_name="gemini-2.0-flash-lite-preview-02-05")  # gemini-2.0-flash   gemini-2.0-flash-lite-preview-02-05   gemini-2.5-pro-exp-03-25  # gemini-2.0-flash-exp
        print(f"{i+1}/{len(files)}: {answer_ai}")
        answer_ai = get_json(answer_ai)
        if isinstance(answer_ai, dict):
            results_df.loc[len(results_df)] = [file, plaintiff, answer_ai.get("brand"), answer_ai.get("confidence")]        
        else:
            results_df.loc[len(results_df)] = [file, plaintiff, answer_ai, answer_ai]
        
    results_df.to_excel("D:/Documents/Programing/TRO/USside/Documents/AssessFiles/For AI/resultsFlash20.xlsx", index=False)



async def get_brand_gemini_async():
    product_path = "D:/Documents/Programing/TRO/USside/Documents/AssessFiles/For AI/3. Products - SmartCrop"
    
    results_df = pd.DataFrame(columns=["picture", "Plaintiff", "AI_brand", "AI_confidence"])
    files = os.listdir(product_path)
    tasks = []
    semaphore = asyncio.Semaphore(10)
    
    for i, file in enumerate(files):
        prompt = 'This is a product being sold on the internet. What is the brand of the product being sold? How confident are you? Return the answer in a json: {"brand":"coca cola", "confidence": 0.87}. If you do not know the brand, return  {"brand":"unknown", "confidence": 0}'
        tasks.append(sem_task(semaphore, vertex_genai_image_async(prompt, image_path=os.path.join(product_path, file), model_name="gemini-2.5-pro-exp-03-25", useVertexAI=True)))  #  gemini-2.0-flash   gemini-2.0-flash-lite-preview-02-05   gemini-2.5-pro-exp-03-25  # gemini-2.0-flash-exp

    # Use asyncio.gather with return_exceptions=True

    answers = await asyncio.gather(*tasks, return_exceptions=True)

    for i, answer in enumerate(answers):
        # Check if the answer is an exception
        if isinstance(answer, Exception):
            print(f"Task {i} failed with exception: {answer}")
            # Handle the failed task (e.g., log the error, use a default value)
            plaintiff = files[i].replace("_cropped", "").split("_")[-1].split(".")[0]
            results_df.loc[len(results_df)] = [files[i], plaintiff, "error", 0.0]  # Example: default values
        else:
            # Process the successful result
            answer_ai = get_json(answer)
            plaintiff = files[i].replace("_cropped", "").split("_")[-1].split(".")[0]
            if isinstance(answer_ai, dict):
                results_df.loc[len(results_df)] = [files[i], plaintiff, answer_ai.get("brand"), answer_ai.get("confidence")]
            else:
                results_df.loc[len(results_df)] = [files[i], plaintiff, answer_ai, answer_ai]
        

    results_df.to_excel("D:/Documents/Programing/TRO/USside/Documents/AssessFiles/For AI/resultsFlash20Pro.xlsx", index=False)



async def run_check_on_evidence():
    client, bucket = get_cos_client()
    target_file = "85_A_Stanislav Yurievich Osipov.jpg"

    for root, _, files in os.walk(os.path.join(os.getcwd(), "data", "EvidencesJanFeb")):
        for file in files:
            if file != target_file:
                continue

            if file.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp')):
                image_path = os.path.join(root, file)
                
                # Generate a fake check ID
                check_id = f"JanFeb618_{file.split("_")[0]}"

                # Create temporary directory for this check
                with tempfile.TemporaryDirectory() as temp_dir:
                    # Copy the image to the temporary directory
                    temp_image_path = os.path.join(temp_dir, file)
                    with open(image_path, 'rb') as f_in, open(temp_image_path, 'wb') as f_out:
                        f_out.write(f_in.read())

                    # Run the check function
                    local_product_images = [temp_image_path]
                    local_ip_images = []
                    local_reference_images = []
                    description = ""
                    ip_keywords = ""
                    reference_text = ""

                    await check(client, bucket, temp_dir, check_id, local_product_images, local_ip_images, local_reference_images, description, ip_keywords, reference_text)

if __name__ == "__main__":
    asyncio.run(run_check_on_evidence())
    # asyncio.run(run_check_on_evidence())

