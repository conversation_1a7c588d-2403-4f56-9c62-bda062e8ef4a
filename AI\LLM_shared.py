import tiktoken
import json
# from langfuse import Langfuse
# # https://langfuse.com/docs/get-started

# langfuse = Langfuse(
#   secret_key="sk-lf-91e5d198-44f4-4c56-ac6f-733716d43eac",
#   public_key="pk-lf-e7133d29-e8f9-418d-83fa-c809f7e7b77d",
#   host="http://ampere.sergedc.com:3000"
# )

def count_text_tokens(text):
    encoding = tiktoken.encoding_for_model("gpt-4o")  # Using GPT-4 tokenizer as approximation
    return len(encoding.encode(text))


def estimate_image_tokens(image):
    # Rough estimation based on image size
    # Gemini processes images in 768x768 patches
    width, height = image.size
    patches = ((width + 767) // 768) * ((height + 767) // 768)  # Round up division
    # Rough estimate: 256 tokens per patch
    return patches * 256



def get_json(text):
    try:
        # Find the first opening brace
        start = text.find('{')
        if start == -1:
            return ""
        
        # Extract JSON text from start to matching end brace
        json_text = text[start:]
        
        # Find the matching closing brace using proper JSON parsing
        in_string = False
        escape_next = False
        brace_count = 1  # Start with 1 for the opening brace
        end_pos = 0
        
        for i, char in enumerate(json_text[1:], 1):  # Skip the first brace we already counted
            # Handle escape sequences
            if escape_next:
                escape_next = False
                continue
                
            # Check if we're escaping the next character
            if char == '\\':
                escape_next = True
                continue
                
            # Toggle string state only for unescaped quotes
            if char == '"' and not escape_next:
                in_string = not in_string
                continue
                
            # Only count braces when not inside a string
            if not in_string:
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    
                    # If we've found the matching closing brace
                    if brace_count == 0:
                        end_pos = i
                        break
        
        # If we didn't find a matching closing brace
        if brace_count != 0:
            print("Error: No matching closing brace found")
            return ""
            
        # Extract the complete JSON object
        json_str = json_text[:end_pos + 1]
        
        # Fix any unescaped quotes or special characters
        fixed_json = fix_malformed_json(json_str)
        
        # Try to parse the fixed JSON
        try:
            return json.loads(fixed_json)
        except json.JSONDecodeError as e:
            print(f"JSON decode error after fixing: {e}")
            # For debugging
            print(f"Problem at position {e.pos}: {fixed_json[max(0, e.pos-10):min(len(fixed_json), e.pos+10)]}")
            return ""
    
    except Exception as e:
        print(f"get_json error: {e}")
        return ""

def fix_malformed_json(json_str):
    """
    Attempts to fix common issues in malformed JSON strings,
    particularly unescaped quotes within string values.
    """
    # Convert to regular Python string with already-escaped characters
    if isinstance(json_str, bytes):
        json_str = json_str.decode('utf-8')
    
    # Pre-process: Attempt to parse as-is first
    try:
        json.loads(json_str)
        # If it parses successfully, no need to fix anything
        return json_str
    except json.JSONDecodeError:
        # Continue with fixing attempt
        pass
        
    # Initialize state for JSON parsing
    output = []
    in_string = False
    i = 0
    
    while i < len(json_str):
        char = json_str[i]
        
        # Handle existing escape sequences
        if char == '\\' and i + 1 < len(json_str):
            output.append(char)
            output.append(json_str[i+1])
            i += 2
            continue
        
        # Handle double quotes
        if char == '"':
            if not in_string:
                # Starting a string
                in_string = True
                output.append(char)
            else:
                # Check if this is a real closing quote by looking for structural elements after it
                # A real closing quote should be followed by one of: , } ] : or whitespace
                is_closing_quote = False
                
                # Look ahead to see what follows this quote
                j = i + 1
                while j < len(json_str) and json_str[j].isspace():
                    j += 1
                
                if j >= len(json_str) or json_str[j] in [',', '}', ']', ':']:
                    is_closing_quote = True
                
                if is_closing_quote:
                    # This is a legitimate JSON string closing quote
                    in_string = False
                    output.append(char)
                else:
                    # This is a quote inside string content - escape it
                    output.append('\\')
                    output.append(char)
        else:
            # Regular character
            output.append(char)
            
        i += 1
    
    # Return the fixed JSON string
    return ''.join(output)

def get_json_list(text):
    try:
        # Find the first opening bracket
        start = text.find('[')
        if start == -1 or start > text.find('{'):
            return []

        # Find the last closing bracket
        end = text.rfind(']')
        if end == -1 or end < text.rfind('}'):
            return []

        # Extract the content between the brackets
        content = text[start + 1:end]

        # Split the content by commas, but be careful not to split inside JSON objects
        json_objects = []
        brace_count = 0
        start_index = 0
        for i, char in enumerate(content):
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
            elif char == ',' and brace_count == 0:
                json_str = content[start_index:i].strip()
                json_objects.append(json_str)
                start_index = i + 1

        # Add the last JSON object
        json_str = content[start_index:].strip()
        if json_str:
            json_objects.append(json_str)
        
        # Parse each JSON object using get_json
        result = []
        for json_str in json_objects:
            parsed_json = get_json(json_str)
            if parsed_json:  # Check if parsing was successful
                result.append(parsed_json)
        return result

    except Exception as e:
        print(f"get_json_list error: {e}")
        return []
    

def get_report(ai_answer, report_start, report_end):
    """
    Extracts the report from the AI's answer.
    """
    # Find the start of the report
    report_start = ai_answer.find(report_start)
    if report_start == -1:
        report_start = ai_answer.find("**")
        if report_start == -1:
            print(f"get_report error: start not found")
            return ""  # Or handle the error as appropriate for your application

    # Find the end of the report
    report_end = ai_answer.find(report_end)
    if report_end == -1:
        report_end = ai_answer.find('{"')
        if report_end == -1:
            print(f"get_report error: end not found")
            report_end = len(ai_answer)

    # Extract the report
    report = ai_answer[report_start:report_end].strip()
    return report



def get_list(text):
    try:
        # Find the first '[' and the last ']'
        start_index = text.find('[')
        end_index = text.rfind(']')

        if start_index == -1 or end_index == -1 or start_index >= end_index:
            print("The string does not contain a valid bracketed list.")
            return None

        # Extract the substring that is between the first '[' and the last ']'
        text = text[start_index + 1:end_index]

        items = []
        current_item = []
        bracket_level = 0
        in_quotes = False
        quote_char = ''
        escape = False

        for char in text:
            if escape:
                current_item.append(char)
                escape = False
                continue
            
            if char == '\\':
                escape = True
                current_item.append(char)
                continue
            
            if char in ('"', "'"):
                if not in_quotes:
                    in_quotes = True
                    quote_char = char
                elif char == quote_char:
                    in_quotes = False
                else:
                    current_item.append(char)
            elif in_quotes:
                current_item.append(char)
            elif char == '[':
                bracket_level += 1
                current_item.append(char)
            elif char == ']':
                bracket_level -= 1
                current_item.append(char)
            elif char == ',' and bracket_level == 0 and not in_quotes:
                items.append(''.join(current_item).strip())
                current_item = []
            else:
                current_item.append(char)

        if current_item:
            items.append(''.join(current_item).strip())  # Add the last item

        return items
    except Exception as e:
        print(f"get_list for '{text}', error: {e}")
        return None

if __name__ == "__main__":
    # Test when there is double quote in the content
    test_str = '{\n"English": "There appear to be multiple companies named "RYZE, INC." or "RYZE". Here\'s an overview of two prominent ones:\\n\\n*   **RYZE Superfoods:** This company produces mushroom-based beverages, primarily a mushroom coffee alternative. They operate in the health and wellness market, targeting consumers seeking energy, focus, and immune support through superfood ingredients. They were founded in 2020 and are based in Boston, Massachusetts.\\n*   **RYSE Inc.:** This company focuses on smart home automation, specifically smart shades. They operate in the smart home industry, offering retrofit solutions to automate existing window coverings. They have a growing market with expansion into retail stores.",\n"Chinese": "似乎有多家名为"RYZE, INC."或"RYZE"的公司。以下是其中两家著名公司的概述：\\n\\n*   **RYZE Superfoods：** 这家公司生产以蘑菇为基础的饮料，主要是一种蘑菇咖啡替代品。 他们的业务范围是健康和保健市场，目标客户是那些希望通过超级食品成分获得能量、注意力和免疫支持的消费者。 该公司成立于 2020 年，总部位于马萨诸塞州波士顿。\\n*   **RYSE Inc.：** 这家公司专注于智能家居自动化，特别是智能百叶窗。 他们在智能家居行业运营，提供改造解决方案以实现现有窗帘的自动化。 他们的市场不断增长，并扩展到零售店。"\n}'
    json_str = get_json(test_str)
    print(json_str)
    print(json.dumps(json_str))


