import os
import smartcrop
from PIL import Image


def crop_evidences(product_path):
    files = os.listdir(product_path)
    for i, file in enumerate(files):
        # if "_cropped.jpg" in file or os.path.join(product_path, f"{file.split('.')[0]}_cropped.jpg") in files:
        #     continue
        if "_cropped.jpg" in file:
            continue
        image = Image.open(os.path.join(product_path, file))
        cropper = smartcrop.SmartCrop()
        result = cropper.crop(image, 300, 300)

        # Save the top crop first
        top_crop = result['top_crop']
        x = top_crop['x']
        y = top_crop['y']
        width = top_crop['width']
        height = top_crop['height']
        
        # Create and save top cropped image
        top_cropped = image.crop((x, y, x + width, y + height))
        top_cropped = top_cropped.resize((600, 600))
        top_output_path = os.path.join(product_path, f"{file.split('.')[0]}_cropped.jpg")
        if top_cropped.mode == 'RGBA':
            top_cropped = top_cropped.convert('RGB')
        top_cropped.save(top_output_path, 'JPEG', quality=95, optimize=True)

        # save all results
        # for j, crop_data in enumerate(result['crops']):
        #     # Extract crop coordinates
        #     x = crop_data['x']
        #     y = crop_data['y']
        #     width = crop_data['width']
        #     height = crop_data['height']
            
        #     # Create cropped image
        #     cropped_img = image.crop((x, y, x + width, y + height))
        #     # Save the cropped image
        #     output_path = os.path.join(product_path, f"{file.split('.')[0]}_cropped_{j+1}.jpg")
        #     cropped_img.save(output_path)

if __name__ == "__main__":
    crop_evidences("D:/Documents/Programing/TRO/USside/Documents/AssessFiles/For AI/Products")

