"""
Reverse check endpoint for comparing IP assets against product images.
"""

import psycopg2, os
from fastapi import APIRouter, Depends
from qdrant_client.http import models
from typing import List, Dict, Any

from models.schemas import ReverseCheckRequest, ReverseCheckResponse, IPAssetInfringement, ProductPointInfringement
from utils.auth import verify_token
from utils.db import get_db_connection
from services.qdrant_service import upsert_ip_assets, query_batch_points

router = APIRouter()

@router.post("/reverse_check", response_model=ReverseCheckResponse, dependencies=[Depends(verify_token)])
async def reverse_check(request: ReverseCheckRequest):
    """
    Reverse Check endpoint.
    Compare newly added IP assets against the database of user-submitted product images.
    
    Args:
        request: The reverse check request.
        
    Returns:
        The reverse check response.
    """
    # Prepare IP assets for upsertion into PostgreSQL
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # Consolidated database insertion
    table_mapping = {"Copyright": "copyrights","Patent": "patents_records","Trademark": "trademarks"}
    for ip_asset in request.ip_assets:
        table_name = table_mapping.get(ip_asset.ip_type)
        if table_name:
            columns = ", ".join(ip_asset.metadata.keys())
            placeholders = ", ".join(["%s"] * len(ip_asset.metadata))
            values = list(ip_asset.metadata.values())
            
            cursor.execute(
                f"INSERT INTO {table_name} (id, {columns}) VALUES (%s, {placeholders}) "
                f"ON CONFLICT (id) DO UPDATE SET {', '.join([f'{k} = EXCLUDED.{k}' for k in ip_asset.metadata.keys()])}",
                [ip_asset.id] + values
            )
    
    # Close connection
    cursor.close()
    conn.close()
    
    # Upsert IP assets into IP_Assets collection
    upsert_ip_assets(request.ip_assets)
    
    # Prepare batch queries for Product_Images collection
    query_requests = []
    
    # Configuration for different IP types' query parameters
    IP_TYPE_QUERY_CONFIG = {"Copyright": {"threshold": 0.75, "limit": 30},
                            "Patent": {"threshold": 0.8, "limit": 25},
                            "Trademark": {"threshold": 0.7, "limit": 35 }}

    querried_ip_assets = []
    for ip_asset in request.ip_assets:
        ip_type = ip_asset.ip_type
        if ip_asset.siglip_vector and len(ip_asset.siglip_vector) == 1024:
            query_requests.append(models.QueryRequest(
                query_vector=("siglip_vector", ip_asset.siglip_vector),
                limit=IP_TYPE_QUERY_CONFIG[ip_type]["limit"],
                score_threshold=IP_TYPE_QUERY_CONFIG[ip_type]["threshold"],
                with_payload=models.PayloadSelectorInclude(include=["client_id", "check_id", "filename"]),
                with_vectors=False
            ))
            querried_ip_assets.append(ip_asset)
    
    # Execute batch query
    batch_results = query_batch_points("Product_Images", query_requests)
    
    # Process results and apply filtering logic
    results = []
    for query_index, ip_asset in enumerate(querried_ip_assets):
        # Collect potential infringements for this IP asset
        potential_infringements = []
        filtered_infringements = []
        
        non_match = 0
        for scored_point in batch_results[query_index]:
            potential_infringements.append({
                "product_point_id": scored_point.id,
                "client_id": scored_point.payload["client_id"],
                "check_id": scored_point.payload["check_id"],
                "filename": scored_point.payload["filename"],
                "score": scored_point.score
            })
            
            # Run the report
            
            
            # Get risk level
            
            
            if risk_level is low:
                non_match += 1
            else:
                non_match = 0
                
                
                # Put the result in the database
            
            if non_match >= 5:
                break
            
            
        # Sort by score in descending order
        filtered_infringements.sort(key=lambda x: x["score"], reverse=True)
        
        # Convert to Pydantic models
        infringements = [
            ProductPointInfringement(
                product_point_id=inf["product_point_id"],
                client_id=inf["client_id"],
                check_id=inf["check_id"],
                score=inf["score"]
            )
            for inf in filtered_infringements
        ]
        
        # Add to results if there are any infringements
        if infringements:
            results.append(IPAssetInfringement(
                input_ip_asset_id=ip_asset.id,
                potential_infringements=infringements
            ))
        
        ip_asset_index += 1
    
    return ReverseCheckResponse(results=results)


async def get_reverse_check_report(check_id, report_prompt, IP_Url, ip_owner, reg_no, ip_file_local, query_image_path):
    prompt_list = [
        ("text", report_prompt),
        ("text", f'\n\nAfter the report, conclude with regards to the risk of infringement with {{"final_answer": "xx"}} where xx is a score between 0 and 10 where 0 is very low risk, and 10 is very high risk.'),
        ("text", f"\n\nCopyright Registered Image from '{ip_owner}' with registration number '{reg_no}':"),
        ("image_path", ip_file_local),
        ("text", "\n\nProduct Image:"),
        ("image_path", query_image_path),
    ]

    Query_URL = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/checks/{check_id}/query/{os.path.basename(query_image_path)}"
    image_url = ["", "", "", IP_Url.replace(" ", "%20").replace("http", "https"), "", Query_URL.replace(" ", "%20").replace("http", "https")]

    ai_answer = await vertex_genai_multi_async(prompt_list, image_url=image_url)
    copyright_risk = get_json(ai_answer)

    try:
        final_answer_score = int(copyright_risk["final_answer"])
    except:
        final_answer_score = 0  # so for "Report not required" we get a score of 0

    if final_answer_score > 0:
        report = get_report(ai_answer, "**1. Image Information:**", "**End of Report**")
        report = "**Copyright Risk Assessment Report**\n\n" + report
        
        copy_tasks = []
        for filename in [result['full_filename'], result['filename']]:
            copy_tasks.append(async_copy_file_with_retry(client, bucket, to_key=f"checks/{check_id}/results/{filename}", from_key=f"plaintiff_images/{plaintiff_id}/high/{filename}", max_retries=3))
        await asyncio.gather(*copy_tasks)

        result["report"] = report
        result["risk_level"] = "高风险" if final_answer_score > 5 else "中风险" if final_answer_score > 2 else "低风险"
        result["risk_description"] = "和其他产权有匹配，并且该产权或产权所有人曾经发起TRO诉讼"
        result["Query_URL"] = Query_URL