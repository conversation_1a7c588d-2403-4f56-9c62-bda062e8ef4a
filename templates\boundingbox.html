{% extends 'layout_ui.html' %}

{% block title %}Bounding Box Visualizer{% endblock %}

{% block head %}
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='boundingbox/boundingbox.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
{% endblock %}

{% block content %}
    <div class="app-container">
        <!-- Input Section - Styled like filters-container -->
        <div class="filters-container"> <!-- Changed from input-container -->
            <h2>Input Parameters</h2>
            <div class="filters-grid"> <!-- Changed from input-grid -->
                <div class="form-group upload-group"> <!-- Group for upload -->
                    <label>Image Upload:</label>
                    <div id="image-upload-area" class="upload-area">
                        <p>Drag & drop an image here, or click to select</p>
                        <input type="file" id="image-upload-input" accept="image/png, image/jpeg, image/jpg, image/webp" style="display: none;">
                        <img id="image-preview" src="#" alt="Image Preview" style="display: none;"/>
                    </div>
                </div>

                <div class="form-group">
                    <label for="prompt-input">Prompt:</label>
                    <input type="text" id="prompt-input" name="prompt" placeholder="e.g., 'Identify all cars'">
                </div>

                <div class="form-group">
                    <label for="model-select">Model Name:</label>
                    <select id="model-select" name="model_name">
                        <option value="gemini-2.0-flash-exp" selected>gemini-2.0-flash-exp</option>
                        <option value="gemini-2.5-pro-exp-03-25">gemini-2.5-pro-exp-03-25</option>
                        <option value="gemini-2.0-flash-001">gemini-2.0-flash-001</option>
                        <option value="gemini-2.5-flash-preview-04-17">gemini-2.5-flash-preview-04-17</option>
                        <option value="other">Other (Specify Below)</option>
                    </select>
                    <input type="text" id="model-other-input" name="model_name_other" placeholder="Enter custom model name" style="display: none; margin-top: 5px;">
                </div>

                <div class="form-group resize-group">
                    <label>Resize Dimensions (px):</label>
                    <div class="resize-inputs">
                         <div>
                            <label for="resize-x-input">Width:</label>
                            <input type="number" id="resize-x-input" name="resize_x" value="640" min="1">
                        </div>
                        <div>
                            <label for="resize-y-input">Height:</label>
                            <input type="number" id="resize-y-input" name="resize_y" value="640" min="1">
                        </div>
                    </div>
                </div>

                <!-- New Form Group for Output Type -->
                <div class="form-group">
                    <label>Output Type:</label>
                    <div class="radio-group">
                        <label>
                            <input type="radio" name="output_type" value="bbox_only" checked>
                            Bounding Box Only
                        </label>
                        <label>
                            <input type="radio" name="output_type" value="bbox_mask">
                            Bounding Box + Segmentation Mask
                        </label>
                    </div>
                </div>
                <!-- End New Form Group -->

            </div>
             <div class="actions-row"> <!-- Similar to visualizer actions -->
                 <div class="action-buttons">
                    <button id="detect-button" class="btn primary">Detect</button>
                 </div>
            </div>
        </div>

        <!-- Output Section - Styled like cases-container -->
        <div class="cases-container"> <!-- Changed from results-container -->
            <h2>Results</h2>
            <div id="loading-indicator" class="loading" style="display: none;">Processing...</div>
            <div id="error-message" class="error-message" style="display: none;"></div> <!-- Changed from error -->

            <div class="result-content"> <!-- Wrapper for results -->
                <div id="result-image-container" class="result-item">
                    <h3>Processed Image:</h3>
                    <img id="result-image" src="#" alt="Processed Image" style="display: none;"/>
                    <p id="no-result-image" style="display: block;">No image processed yet.</p>
                </div>

                <div id="json-output-container" class="result-item">
                    <h3>Raw JSON Output:</h3>
                     <div class="json-header">
                        <button id="copy-json-button" class="btn secondary small" disabled>Copy JSON</button>
                    </div>
                    <pre id="json-output" class="json-output-box">No JSON output yet.</pre>
                </div>

                <!-- New Container for Segmentation Masks -->
                <div id="segmentation-masks-container" class="result-item" style="display: none;">
                    <h3>Segmentation Masks:</h3>
                    <div id="segmentation-masks-grid">
                        <!-- Mask images will be added here by JS -->
                    </div>
                </div>
                <!-- End New Container -->

            </div>
        </div>
    </div>
{% endblock %}

{% block scripts %}
    <script>
        // Ensure the correct menu item is marked active
        document.addEventListener('DOMContentLoaded', function() {
            const menuItem = document.getElementById('menu-boundingbox');
            if (menuItem) {
                menuItem.classList.add('active');
            }

            // Handle 'Other' model selection
            const modelSelect = document.getElementById('model-select');
            const modelOtherInput = document.getElementById('model-other-input');
            if (modelSelect && modelOtherInput) {
                modelSelect.addEventListener('change', function() {
                    if (this.value === 'other') {
                        modelOtherInput.style.display = 'block';
                        modelOtherInput.focus();
                    } else {
                        modelOtherInput.style.display = 'none';
                    }
                });
                // Initial check
                if (modelSelect.value === 'other') {
                     modelOtherInput.style.display = 'block';
                }
            }
        });
    </script>
    <script src="{{ url_for('static', filename='boundingbox/boundingbox.js') }}"></script>
{% endblock %}