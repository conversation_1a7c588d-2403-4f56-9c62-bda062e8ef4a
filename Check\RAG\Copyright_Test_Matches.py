import os
import sys
sys.path.append(os.getcwd())
import numpy as np
import cv2
from tqdm import tqdm
from sklearn.metrics.pairwise import cosine_similarity
from scipy.spatial.distance import hamming
from DatabaseManagement.ImportExport import get_table_from_GZ
from Check.RAG.Build_Vector_Store_Copyright import (
    get_efficientnet_embeddings, get_clipv2_embeddings, get_resnet_embeddings, get_vgg_embeddings, 
    get_effnetv2_embeddings, get_inception_resnet_embeddings, get_xception_embeddings, get_convnext_embeddings,
    get_swin_embedding, calculate_dhash, get_orb_features, get_sift_features,
    get_enhanced_orb_features, get_enhanced_sift_features, preprocess_image_resnet, preprocess_image_vgg, 
    resnet152v2, vgg19, get_google_multimodal_embeddings
)

def load_existing_dataset():
    path = os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', "EmbeddingsMultiModel.npy")
    return np.load(path) if os.path.exists(path) else None

def find_top_matches(query_feat, dataset_feats, model_name, top_k=3):
    """Find top matches based on model-specific similarity metrics"""
    if model_name in ['efficientnet', 'clipv2', 'resnet', 'vgg', 'effnetv2',
                     'inception_resnet', 'xception', 'convnext', 'resnet152v2',
                     'vgg19', 'swin', 'google_multimodal']:
        # CNN embeddings use cosine similarity
        scores = cosine_similarity([query_feat], dataset_feats)[0]
    elif 'dhash' in model_name:
        # Hamming distance for hashes
        scores = [hamming(query_feat, x) for x in dataset_feats]
        scores = 1 - np.array(scores)
    elif 'orb' in model_name or 'sift' in model_name or 'orb_edges' in model_name or 'sift_edges' in model_name:
        # Feature matching using brute-force matcher
        scores = []
        bf = cv2.BFMatcher(cv2.NORM_HAMMING if 'orb' in model_name else cv2.NORM_L2)
        for feat in dataset_feats:
            matches = bf.match(query_feat, feat)
            good_matches = sum(1 for m in matches if m.distance < 50)
            scores.append(good_matches / max(len(query_feat), 1))
    else:
        raise ValueError(f"Unknown model type: {model_name}")

    top_indices = np.argsort(scores)[-top_k:][::-1]
    return [(int(i), float(scores[i])) for i in top_indices]



def combine_models(existing_data, features, model1_name, weight1, model2_name, weight2, new_model_name):
    """
    Combines two models using weighted cosine similarity scores.

    Args:
        existing_data: The loaded dataset containing embeddings.
        features: Dictionary of pre-calculated features for the query image.
        model1_name: Name of the first model (e.g., 'efficientnet').
        weight1: Weight for the first model's scores.
        model2_name: Name of the second model (e.g., 'clipv2').
        weight2: Weight for the second model's scores.
        new_model_name: Name of the combined model (e.g., '1.4eff+c.v2').

    Returns:
        A tuple: (new_model_name, list_of_top_matches)
    """
    dataset_feats1 = existing_data[f'{model1_name}_embedding']
    dataset_feats2 = existing_data[f'{model2_name}_embedding']
    scores1 = cosine_similarity([features[model1_name]], dataset_feats1)[0]
    scores2 = cosine_similarity([features[model2_name]], dataset_feats2)[0]
    combined_scores = weight1 * scores1 + weight2 * scores2
    top_k = 3
    top_indices = np.argsort(combined_scores)[-top_k:][::-1]
    top_matches = [(int(i), float(combined_scores[i])) for i in top_indices]

    return (
        new_model_name,
        [(existing_data['filename'][i], score,
          existing_data['docket'][i], existing_data['plaintiff_name'][i])
         for i, score in top_matches]
    )


def process_product_images(product_dir):
    existing_data = load_existing_dataset()
    if existing_data is None:
        raise ValueError("No existing embeddings found. Run Build_Vector_Store_Copyright first")

    # Load existing results if available
    results_path = os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', "MultiModelBestMatches.npy")
    existing_results = np.load(results_path) if os.path.exists(results_path) else None
    needs_reprocessing = False
    
    if existing_results is not None:
        # Check if model structure matches current dataset
        current_num_models = len(existing_data.dtype) - 6
        if existing_results['models'].shape[1] != current_num_models:
            print("Model structure mismatch detected. Reprocessing all product images.")
            needs_reprocessing = True
        else:
            print(f"Found existing results with {len(existing_results)} entries")

    product_files = [f for f in os.listdir(product_dir) if os.path.isfile(os.path.join(product_dir, f))]
    # product_files = product_files[:3]
    
    # Filter based on existing results
    if existing_results is not None and not needs_reprocessing:
        processed_files = set(existing_results['product_filename'])
        product_files = [f for f in product_files if f not in processed_files]

    if not product_files and not needs_reprocessing:
        print("No new product images to process")
        return existing_results if existing_results is not None else None

    # Define structure for storing matches
    dtype = [
        ('product_filename', 'U500'),
        ('models', [
            ('model_name', 'U100'),
            ('top_matches', [
                ('filename', 'U500'),
                ('score', 'float32'),
                ('docket', 'U100'),
                ('plaintiff_name', 'U500'),
                ('qualityAssessment', 'float32')
            ], (3,))
        ], (len(existing_data.dtype)-6 + 1,))  # Number of models per product (1 for combined model)
    ]
    
    results = np.zeros(len(product_files), dtype=dtype)
    
    for idx, img_file in enumerate(tqdm(product_files, desc="Processing products")):
        img_path = os.path.join(product_dir, img_file)
        
        # Generate all features/embeddings for current product image
        features = {
            'efficientnet': get_efficientnet_embeddings([img_path])[0],
            'clipv2': get_clipv2_embeddings([img_path], "image")[0],
            'resnet': get_resnet_embeddings([img_path])[0],
            'vgg': get_vgg_embeddings([img_path])[0],
            'dhash': calculate_dhash(img_path),
            'orb': get_orb_features(img_path),
            'sift': get_sift_features(img_path),
            'orb_edges': get_enhanced_orb_features(img_path),
            'sift_edges': get_enhanced_sift_features(img_path),
            'effnetv2': get_effnetv2_embeddings([img_path])[0],
            'inception_resnet': get_inception_resnet_embeddings([img_path])[0],
            'xception': get_xception_embeddings([img_path])[0],
            'convnext': get_convnext_embeddings([img_path])[0],
            'resnet152v2': resnet152v2.predict(np.expand_dims(preprocess_image_resnet(img_path), axis=0))[0],
            'vgg19': vgg19.predict(np.expand_dims(preprocess_image_vgg(img_path), axis=0))[0],
            'swin': get_swin_embedding(img_path),
            'google_multimodal': get_google_multimodal_embeddings([img_path])
        }
        
        # Compare against all models
        model_matches = []
        for model_name in features:
            # Handle different storage naming conventions
            if model_name in ['orb', 'sift']:
                # These handcrafted features use '_features' suffix in storage
                dataset_feats = existing_data[f'{model_name}_features']
            elif model_name in ['orb_edges', 'sift_edges', 'dhash']:
                # Edge-enhanced features use direct naming
                dataset_feats = existing_data[model_name]
            else:
                # CNN models use '_embedding' suffix
                dataset_feats = existing_data[f'{model_name}_embedding']
            
            top_matches = find_top_matches(features[model_name], dataset_feats, model_name)
            
            # Store match details
            model_matches.append((
                model_name,
                [(existing_data['filename'][i], score, 
                  existing_data['docket'][i], existing_data['plaintiff_name'][i]) 
                 for i, score in top_matches]
            ))

                # --- Use the combine_models function ---
        combined_model_result = combine_models(existing_data, features, 'efficientnet', 1.4, 'clipv2', 1, '1.4eff+c.v2')
        model_matches.append(combined_model_result)
        # --- End of combined model handling ---
        
        # Store all models for this product
        results[idx]['product_filename'] = img_file
        for model_idx, (model_name, matches) in enumerate(model_matches):
            results[idx]['models'][model_idx]['model_name'] = model_name
            for match_idx, (filename, score, docket, plaintiff_name) in enumerate(matches):
                results[idx]['models'][model_idx]['top_matches'][match_idx] = (
                    filename,
                    score,
                    docket,
                    plaintiff_name,
                    np.nan  # qualityAssessment
                )
        
        # Save chunk periodically
        if idx % 10 == 0 or idx == len(product_files)-1:
            if existing_results is not None and not needs_reprocessing:
                combined = np.concatenate([existing_results, results[:idx+1]])
            else:
                combined = results[:idx+1] if existing_results is None else results[:idx+1]
            np.save(results_path, combined)

    # Final save with all results
    if existing_results is not None and not needs_reprocessing:
        final_results = np.concatenate([existing_results, results])
    else:
        final_results = results
    
    np.save(results_path, final_results)
    print(f"Saved updated results with {len(final_results)} entries")
    return final_results


if __name__ == "__main__":
    import time
    start_time = time.time()
    process_product_images(os.path.join(os.getcwd(), 'Documents', 'AssessFiles', 'CopyrightTest'))
    end_time = time.time()
    print(f"Total time taken: {end_time - start_time:.1f} seconds")