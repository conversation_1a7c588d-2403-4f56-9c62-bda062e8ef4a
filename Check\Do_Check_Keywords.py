from AI.LLM_shared import get_json, get_report
from AI.GC_VertexAI import vertex_genai_multi_async
from Check.Find_In_List import find_brand_in_trademark_text, find_name_in_plaintiff_list
from langfuse import observe



async def create_keyword_report(temp_dir, check_id, match_item, brand_name, parent_company, plaintiff_df, client, bucket, report_prompt):

    prompt_list = [
        ("text", report_prompt),
        ("text", f'\n\nAfter the report, conclude as to whether there is a high risk of infringement with either {{"final_answer": "yes"}} or {{"final_answer": "no"}}'),
        ("text", f"\n\nTrademark Registered with Registration Number {match_item['reg_no'][0]} and International Classification {match_item['int_cls_list']}: {match_item['....']}"),
        ("text", f"\n\nWords used by the online seller: {match_item['....']}"),
    ]

    ai_answer = await vertex_genai_multi_async(prompt_list, model_name="gemini-2.0-flash-exp")
    report = get_report(ai_answer, "**1. Mark Information:**", "**End of Report**")
    report = "**Trademark Risk Assessment Report**\n\n" + report
    is_trademark = get_json(ai_answer)

    if is_trademark["final_answer"] == "yes": 
        if "full_filename" in match_item:
            for filename in [match_item["full_filename"][0], match_item["filename"]]:
                client.copy_object(
                    Bucket=bucket,
                    Key=f"checks/{check_id}/results/{filename}",
                    CopySource={
                        'Bucket': bucket,
                        'Key': f"plaintiff_images/{match_item['plaintiff_id']}/high/{filename}", 
                        'Region': 'ap-guangzhou'
                    }
                )

        return {
            "ip_type": "Trademark",
            "ip_owner": match_item["plaintiff_name"],
            "plaintiff_id": str(match_item["plaintiff_id"]),
            "report": report,
            "risk_level": "高风险",  # in english: "high risk"
            "risk_description": "和其他产权有匹配，并且该产权或产权所有人曾经发起TRO诉讼",  # in english: "and the IP or IP owner has previously filed a TRO lawsuit"
            "ip_image": [match_item["full_filename"], match_item["filename"]],
            # "Query_URL": Query_URL   # Should be the query word
        }
    else:
        parts = [part for part in [brand_name, parent_company] if part]
        ip_owner = " - ".join(parts) if parts else "No brand or parent company found"
        return {
            "ip_type": "Trademark",
            "ip_owner": ip_owner,
            "risk_level": "中风险",  # in english: "medium risk"
            "risk_description": "和其他产权有匹配",  # in english: match with other IP
            # "Query_URL": Query_URL  # Should be the query word
        }