# app_boundingbox.py
import asyncio
import base64
import io
from flask import Flask, request, jsonify, render_template, Response, Blueprint
from auth_decorators import login_required
from AI.BoundingBox import get_image_with_bounding_boxes_async
from PIL import Image
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create a Blueprint for bounding box routes
boundingbox_bp = Blueprint('boundingbox', __name__, template_folder='templates')

@boundingbox_bp.route('/boundingbox')
@login_required
def bounding_box_page():
    """Renders the bounding box page."""
    # Note: 'boundingbox.html' will be created in a subsequent step.
    return render_template('boundingbox.html')

@boundingbox_bp.route('/api/boundingbox/process', methods=['POST'])
@login_required
def process_bounding_box_image():
    """Processes an uploaded image to add bounding boxes based on a prompt."""
    if 'image' not in request.files:
        logger.error("No image file provided in the request.")
        return jsonify({"success": False, "error": "No image file provided."}), 400

    file = request.files['image']
    prompt = request.form.get('prompt')
    model_name = request.form.get('model_name', 'gemini-1.5-flash-latest') # Default model
    output_type = request.form.get('output_type', 'bbox_only') # Get output type, default to bbox_only

    if not file or file.filename == '':
        logger.error("Empty image file provided.")
        return jsonify({"success": False, "error": "No selected file or empty file."}), 400

    if not prompt:
        logger.error("No prompt provided.")
        return jsonify({"success": False, "error": "Prompt is required."}), 400

    try:
        resize_x = int(request.form.get('resize_x', 640))
        resize_y = int(request.form.get('resize_y', 640))
    except ValueError:
        logger.warning("Invalid resize dimensions provided, using default 640x640.")
        resize_x, resize_y = 640, 640

    try:
        image_bytes = file.read()
        # Determine image format (simple check based on filename, could be more robust)
        filename = file.filename.lower()
        if '.' in filename:
            ext = filename.rsplit('.', 1)[1]
            if ext in ['jpg', 'jpeg']:
                image_format = 'JPEG'
                output_format = 'JPEG'
                mime_type = 'image/jpeg'
            elif ext == 'png':
                image_format = 'PNG'
                output_format = 'PNG'
                mime_type = 'image/png'
            elif ext == 'webp':
                 image_format = 'WEBP'
                 output_format = 'PNG' # Output as PNG for broader compatibility
                 mime_type = 'image/png'
            else:
                 # Try opening with PIL to detect format, default to PNG output
                 img = Image.open(io.BytesIO(image_bytes))
                 image_format = img.format or 'PNG' # Use detected format or default
                 output_format = 'PNG'
                 mime_type = 'image/png'
                 logger.info(f"Detected image format: {image_format}, using PNG for output.")

        else:
            # If no extension, default to trying PNG
            image_format = 'PNG'
            output_format = 'PNG'
            mime_type = 'image/png'
            logger.warning("No file extension found, assuming PNG.")


        logger.info(f"Processing image: {file.filename}, Prompt: '{prompt}', Model: {model_name}, Resize: {resize_x}x{resize_y}, Format: {image_format}, Output Type: {output_type}")

        # Initialize response variables
        success = False
        result_image_bytes = None
        json_output = None
        segmentation_masks = None # Initialize segmentation masks
        error_message = None
        image_data_url = None

        try:
            # Determine if masks should be included based on output_type
            include_masks = (output_type == 'bbox_mask')

            # Call the asynchronous AI function (now returns 3 values)
            result_image_bytes, json_output, segmentation_masks = asyncio.run(get_image_with_bounding_boxes_async(
                image_bytes=image_bytes,
                image_format=image_format, # Pass detected/assumed format
                prompt=prompt,
                model_name=model_name,
                resize_x=resize_x,
                resize_y=resize_y,
                include_masks=include_masks # Pass the flag to the AI function
            ))

            # --- Start Diagnostic Logging ---
            if result_image_bytes:
                logger.info(f"Type of result_image_bytes: {type(result_image_bytes)}")
                logger.info(f"First 100 bytes of result_image_bytes: {result_image_bytes[:100]}")
            else:
                logger.warning("result_image_bytes is None before encoding.")
            # --- End Diagnostic Logging ---

            # If the call succeeds, set success and prepare image data
            if result_image_bytes:
                success = True
                # error_message remains None
                # Encode the processed image bytes to base64
                encoded_image = base64.b64encode(result_image_bytes).decode('utf-8')
                image_data_url = f"data:{mime_type};base64,{encoded_image}"
                logger.info(f"Successfully processed image for prompt: '{prompt}'. Masks requested: {include_masks}, Masks returned: {bool(segmentation_masks)}")
            else:
                # If AI call returned None for image bytes, consider it a failure
                success = False
                error_message = "AI processing failed to return an image."
                logger.error(f"AI processing failed for prompt: '{prompt}'. JSON output: {json_output}")
                # Ensure other results are None
                json_output = None
                segmentation_masks = None
                image_data_url = None

        except Exception as e:
            logger.exception(f"An error occurred during bounding box processing: {e}")
            # success remains False
            result_image_bytes = None # Ensure these are None on error
            json_output = None
            segmentation_masks = None # Ensure masks are None on error
            image_data_url = None # Ensure image data is None on error
            error_message = f"An error occurred during processing: {str(e)}" # Capture error

    except Exception as e: # Catch errors from file reading/setup before AI call
        logger.exception(f"An unexpected error occurred before AI processing: {e}")
        # success is already False, other data is None
        error_message = f"An unexpected server error occurred: {str(e)}"

    # Construct and return the final JSON response
    response_data = {
        "success": success,
        "image_data": image_data_url, # Will be None if processing failed or result_image_bytes was None
        "json_output": json_output,   # Will be None if processing failed
        "segmentation_masks": segmentation_masks, # Add masks to the response
        "error": error_message        # Will be None if processing succeeded
    }
    status_code = 200 if success else 500
    return jsonify(response_data), status_code

def init_boundingbox_routes(app: Flask):
    """Registers the bounding box blueprint with the Flask app."""
    app.register_blueprint(boundingbox_bp)
    return app