#!/usr/bin/env python3
"""
Test script for the statistics system.
Run this to verify that the statistics system is working correctly.
"""

import os
import sys
from datetime import datetime

def test_database_connection():
    """Test PostgreSQL database connection."""
    try:
        from DatabaseManagement.StatisticsDB import get_postgres_connection
        conn = get_postgres_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        cursor.close()
        conn.close()
        print("✅ PostgreSQL connection successful")
        return True
    except Exception as e:
        print(f"❌ PostgreSQL connection failed: {e}")
        return False

def test_table_creation():
    """Test statistics table creation."""
    try:
        from DatabaseManagement.StatisticsDB import init_statistics_table
        init_statistics_table()
        print("✅ Statistics table initialization successful")
        return True
    except Exception as e:
        print(f"❌ Statistics table initialization failed: {e}")
        return False

def test_statistics_collection():
    """Test statistics collection."""
    try:
        from Statistics.StatisticsCollector import collect_generate_report_metrics
        metrics = collect_generate_report_metrics()
        print(f"✅ Statistics collection successful - collected {len(metrics)} metrics")
        
        # Print first few metrics for verification
        for i, metric in enumerate(metrics[:3]):
            print(f"   {metric['metric_name']}: {metric['metric_value']}")
        
        return True
    except Exception as e:
        print(f"❌ Statistics collection failed: {e}")
        return False

def test_statistics_storage():
    """Test storing statistics in database."""
    try:
        from DatabaseManagement.StatisticsDB import store_statistics_batch
        from Statistics.StatisticsCollector import collect_generate_report_metrics
        
        # Collect a few metrics
        metrics = collect_generate_report_metrics()[:3]  # Just test with first 3
        
        # Store them
        store_statistics_batch(metrics)
        print(f"✅ Statistics storage successful - stored {len(metrics)} metrics")
        return True
    except Exception as e:
        print(f"❌ Statistics storage failed: {e}")
        return False

def test_statistics_retrieval():
    """Test retrieving statistics from database."""
    try:
        from DatabaseManagement.StatisticsDB import get_latest_statistics
        
        df = get_latest_statistics()
        print(f"✅ Statistics retrieval successful - found {len(df)} metrics")
        
        if len(df) > 0:
            print("   Latest metrics:")
            for _, row in df.head(3).iterrows():
                print(f"   {row['metric_name']}: {row['metric_value']}")
        
        return True
    except Exception as e:
        print(f"❌ Statistics retrieval failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing Statistics System")
    print("=" * 50)
    
    # Check environment variables
    required_env_vars = ['POSTGRES_HOST', 'POSTGRES_PORT', 'POSTGRES_USER', 'POSTGRES_PASSWORD', 'POSTGRES_DB']
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        print("Please set these environment variables before running the test.")
        return False
    
    print("✅ All required environment variables are set")
    
    # Run tests
    tests = [
        ("Database Connection", test_database_connection),
        ("Table Creation", test_table_creation),
        ("Statistics Collection", test_statistics_collection),
        ("Statistics Storage", test_statistics_storage),
        ("Statistics Retrieval", test_statistics_retrieval),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 Testing {test_name}...")
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append(False)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ PASS" if results[i] else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The statistics system is ready to use.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
