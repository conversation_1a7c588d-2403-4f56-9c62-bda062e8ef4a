"""
Database utilities for the API.
"""

import psycopg2
import psycopg2.extras
from utils.config import POSTGRES_HOST, POSTGRES_PORT, POSTGRES_USER, POSTGRES_PASSWORD, POSTGRES_DB

def get_db_connection():
    """
    Get a connection to the PostgreSQL database.
    
    Returns:
        A connection to the PostgreSQL database.
    """
    conn = psycopg2.connect(
        host=POSTGRES_HOST,
        port=POSTGRES_PORT,
        user=POSTGRES_USER,
        password=POSTGRES_PASSWORD,
        dbname=POSTGRES_DB
    )
    conn.autocommit = True
    return conn

def get_ip_asset_metadata(asset_ids):
    """
    Get metadata for IP assets from PostgreSQL.
    
    Args:
        asset_ids: A list of IP asset IDs.
        
    Returns:
        A dictionary mapping IP asset IDs to their metadata.
    """
    conn = get_db_connection()
    cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
    
    # Convert asset_ids to a list of UUIDs
    uuids = list(asset_ids)
    
    # Query trademarks
    cursor.execute(
        "SELECT * FROM trademarks WHERE id = ANY(%s::uuid[])",
        (uuids,)
    )
    trademarks = {str(row['id']): dict(row) for row in cursor.fetchall()}
    
    # Query patents
    cursor.execute(
        "SELECT * FROM patents_records WHERE id = ANY(%s::uuid[])",
        (uuids,)
    )
    patents = {str(row['id']): dict(row) for row in cursor.fetchall()}
    
    # Query copyrights
    cursor.execute(
        "SELECT * FROM copyrights WHERE id = ANY(%s::uuid[])",
        (uuids,)
    )
    copyrights = {str(row['id']): dict(row) for row in cursor.fetchall()}
    
    # Close connection
    cursor.close()
    conn.close()
    
    # Combine all metadata
    metadata = {}
    metadata.update({id: {"type": "trademark", "data": data} for id, data in trademarks.items()})
    metadata.update({id: {"type": "patent", "data": data} for id, data in patents.items()})
    metadata.update({id: {"type": "copyright", "data": data} for id, data in copyrights.items()})
    
    return metadata
