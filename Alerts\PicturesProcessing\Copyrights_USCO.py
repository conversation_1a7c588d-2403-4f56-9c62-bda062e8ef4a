import os
import sys
from typing import Optional
sys.path.append(os.getcwd())
import os
import pandas as pd # Added import
from logdata import log_message
from Alerts.PicturesProcessing.Copyrights_Google import get_copyright_images_from_google
from AI.GC_VertexAI import vertex_genai_multi, vertex_genai_multi_async # Added vertex_genai_multi_async
from AI.LLM_shared import get_json, get_list # Added get_list
from langfuse import observe
import langfuse
from IP.Copyrights.Copyright_USCO import get_info_from_USCO_using_reg_no, find_best_search_combination # Added import
from Alerts.IPTrackingManager import IPTrackingManager # Added import
from typing import List
from IP.Copyrights.Copyright_USCO import scrape_USCO # For by_name search
from Alerts.Plaintiff import generate_search_terms_for_ip # For by_name search
import Common.Constants as Constants
from Alerts.PicturesProcessing.ProcessPicturesShared import find_complaint_pdf_path # Added import
from Alerts.Chrome_Driver import get_driver
from DatabaseManagement.ImportExport import get_table_from_GZ

# VA == Visual Art
# TX == Computer File
# PA == Performance Art (Motion Picture)


@observe(capture_input=False, capture_output=False)
def get_copyright_art_info(df, index, reg_nos_to_check: List[str], ip_manager: IPTrackingManager, pdf_path_context: Optional[str] = None):
    """
    For a list of copyright registration numbers, attempts to find artist/title information and saves to DB.
    First, optionally uses LLM with a context PDF. Then, falls back to scraping USCO.
    Updates IPTrackingManager with found information (typically as 'pictures_not_found' if image itself isn't available).

    Args:
        df: DataFrame containing case data.
        index: Index of the current case in the DataFrame.
        reg_nos_to_check: List of registration numbers to get info for.
        ip_manager: Instance of IPTrackingManager.
        pdf_path_context: Optional path to a PDF to use as context for LLM.
    """
    if not reg_nos_to_check:
        log_message("get_info_from_usco: No registration numbers provided to check.", level="INFO")
        return

    log_message(f"Attempting to find claimant/title for {len(reg_nos_to_check)} reg_nos.", level="INFO")
    
    reg_nos_with_title_in_df = ip_manager.copyright_dataframe[~ip_manager.copyright_dataframe["title"].isna()]['registration_number'].tolist()
    reg_nos_needing_external_check = [reg_no for reg_no in reg_nos_to_check if reg_no not in reg_nos_with_title_in_df]

    if not reg_nos_needing_external_check:
        log_message("All registration numbers were found in IP manager cache or database. No external checks needed.", level="INFO")
        # Update Langfuse observation for early exit if all handled by cache/DB
        langfuse.get_client().update_current_span(output="All registration numbers were found in IP manager cache or database. No external checks needed.")
        return

    reg_nos_still_missing_details = list(reg_nos_needing_external_check) # Work on a copy for LLM/USCO

    # Stage 1: Try LLM with PDF context if provided
    # if pdf_path_context and os.path.exists(pdf_path_context) and reg_nos_still_missing_details:
    #     log_message(f"Using LLM with PDF context {os.path.basename(pdf_path_context)} for {len(reg_nos_still_missing_details)} reg_nos.", level="INFO")
    #     prompt_llm_context = """
    #     You are an expert in copyright registration numbers.
    #     You are given a list of copyright registration numbers and a document related to the court filings. For each number, you need to find the artist name (often called "Author") and the artwork name (often called "Title of work") in the document.
    #     You return your answer in a JSON format with the following keys: {
    #     """
    #     for reg_no_ctx in reg_nos_still_missing_details:
    #         prompt_llm_context += f'"{reg_no_ctx}": ["artist_name", "artwork_name"], '
    #     prompt_llm_context += "}. Do not include REDACTED registration numbers in your answer. If a registration number has no associated artist/artwork name in this document, leave its list empty []. These are the documents: "

    #     prompt_list_ctx = [("text", prompt_llm_context), ("pdf_path", pdf_path_context)]
    #     ai_answer_ctx = vertex_genai_multi(prompt_list_ctx, model_name=Constants.SMART_MODEL_FREE)
    #     ai_answer_json_ctx = get_json(ai_answer_ctx)

    #     if isinstance(ai_answer_json_ctx, dict):
    #         reg_nos_found_by_llm_ctx = []
    #         for reg_no, item_list in ai_answer_json_ctx.items():
    #             if reg_no in reg_nos_still_missing_details and isinstance(item_list, list) and len(item_list) == 2:
    #                 artist_name, artwork_name = item_list
    #                 artist_name = str(artist_name) if artist_name is not None else ""
    #                 artwork_name = str(artwork_name) if artwork_name is not None else ""

    #                 log_message(f"LLM (context PDF) found for {reg_no}: Artist='{artist_name}', Title='{artwork_name}'", level="DEBUG")
    #                 ip_manager.add_copyright_to_dataframe({'registration_number': reg_no, 'names': artist_name, 'title': artwork_name}, location_id=f"llm_ctx-{reg_no}")
    #                 reg_nos_found_by_llm_ctx.append(reg_no)

    #         reg_nos_still_missing_details = [r for r in reg_nos_still_missing_details if r not in reg_nos_found_by_llm_ctx]
    #     else:
    #         log_message(f"LLM (context PDF) did not return a valid JSON dict. Proceeding to USCO for all remaining.", level="WARNING")

    # Stage 2: Fallback to USCO for remaining numbers
    if reg_nos_still_missing_details:
        log_message(f"Checking USCO for details on {len(reg_nos_still_missing_details)} remaining reg_nos...", level="INFO")
        for reg_no_usco in reg_nos_still_missing_details:
            usco_info = get_info_from_USCO_using_reg_no(reg_no_usco)
            if usco_info and isinstance(usco_info, dict):
                log_message(f"Found USCO details for {reg_no_usco}: Artist='{str(usco_info.get("names", ""))}', Title='{str(usco_info.get("title", ""))}'", level="INFO")
                ip_manager.add_copyright_to_dataframe(usco_info)
            else:
                log_message(f"Could not find USCO details for {reg_no_usco}. Adding placeholders.", level="INFO")
                ip_manager.add_copyright_to_dataframe({'registration_number': reg_no_usco})
    else:
        log_message("No registration numbers remaining for USCO check.", level="INFO")
    langfuse.get_client().update_current_span(
        input={
            "CaseIndex": index,
            "NumRegNosToCheck": len(reg_nos_to_check),
            "PDFContextProvided": pdf_path_context is not None
        },
        output={
            "CopyrightPicturesNotFound:": ip_manager.get_copyrights_without_images()
        }
    )


@observe(capture_input=False, capture_output=False)
async def get_copyright_data_by_name(
    df, 
    index, 
    case_images_directory: str, # For Google search output
    plaintiff_df, 
    ip_manager: IPTrackingManager, 
    case_directory: str, # For finding complaint PDF
    config: dict # For potential LLM configs, and passing to Google search
):
    """
    Searches for copyrights by plaintiff name using USCO scraping and Google Images.

    Note: This function now manages the Selenium driver.
    Args:
        df: DataFrame containing case data.
        index: Index of the current case in the DataFrame.
        case_images_directory: Directory to save extracted images.
        plaintiff_df: DataFrame containing plaintiff information.
        ip_manager: Instance of IPTrackingManager to track IP processing state.
        case_directory: Path to the case directory (used for finding complaint PDF).
        config: Configuration dictionary.
    """
    log_message(f"        Initiating copyright search by name for case index {index}...", level='INFO')
    plaintiff_id = df.at[index, "plaintiff_id"]
    case_id = df.at[index, Constants.CASE_ID_COL] # Assuming Constants.CASE_ID_COL holds the column name for case_id

    main_plaintiff_name_row = plaintiff_df[plaintiff_df["id"] == plaintiff_id]

    if main_plaintiff_name_row.empty:
        log_message(f"        - Main Plaintiff ID {plaintiff_id} not found in plaintiff_df. Cannot effectively search by name.", level='WARNING')
        # Update Langfuse observation for early exit
        langfuse.get_client().update_current_span(output={"USCOResultsCount": 0, "GoogleImagesProcessedCount": 0, "ReasonForExit": "Main plaintiff not found"})
        return False # Or handle as appropriate
    main_plaintiff_name = main_plaintiff_name_row["plaintiff_name"].values[0]

    plaintiff_names_json_str = df.at[index, "plaintiff_names"]
    final_search_terms = generate_search_terms_for_ip(main_plaintiff_name, plaintiff_names_json_str)

    if not final_search_terms:
        log_message(f"        - No suitable search terms generated for copyright search by name.", level='INFO')
        langfuse.get_client().update_current_span(output={"USCOResultsCount": 0, "GoogleImagesProcessedCount": 0, "ReasonForExit": "No search terms generated"})
        return False

    driver = None
    copyright_df_scraped = None # DataFrame directly from scrape_USCO
    prepared_copyright_df = pd.DataFrame() # Initialize as empty
    relevant_copyright_df = pd.DataFrame() # DataFrame after LLM filtering, initialized empty
    processed_google_count = 0
    best_search_params = None
    llm_selected_reg_nos_list = []

    # Initialize prepared_copyright_df, attempt to load from DB first
    db_copyrights_df = ip_manager.copyright_dataframe

    if db_copyrights_df is not None and not db_copyrights_df.empty:
        log_message(f"        - Loaded {len(db_copyrights_df)} copyrights from database for plaintiff {plaintiff_id}.", level="INFO")
        prepared_copyright_df = db_copyrights_df.copy()
        # Data from DB is sufficient, skip USCO scrape.
        # copyright_df_scraped remains None. best_search_params remains None.
        pass # Proceed to LLM filtering with prepared_copyright_df from DB
    else:
        log_message(f"        - No copyrights found in database for plaintiff {plaintiff_id}. Proceeding with USCO scrape.", level="INFO")
        try:
            driver = get_driver() # Initialize driver here
            best_search_params = find_best_search_combination(driver=driver,final_search_terms=final_search_terms)
            # copyright_df_scraped = None # Already initialized above

            if best_search_params:
                log_message(f"        - Performing full scrape using best combination: Name='{best_search_params['name']}', Column='{best_search_params['column_name']}', Type='{best_search_params['type_of_query']}'", level='INFO')
                copyright_df_scraped = scrape_USCO(
                    driver=driver,
                    name=best_search_params['name'],
                    column_name=best_search_params['column_name'],
                    type_of_query=best_search_params['type_of_query'],
                    ip_manager=ip_manager,
                    max_results=100
                )

                if copyright_df_scraped is not None and not copyright_df_scraped.empty:
                    for _, row in copyright_df_scraped.iterrows():
                        ip_manager.add_copyright_to_dataframe(row.to_dict())
                    df.at[index, 'validation_status'] = 'review_required'

                    log_message(f"        - Successfully saved/updated and prepared {len(copyright_df_scraped)} scraped copyright records.", level="INFO")
                    # If db_copyrights_df was empty, prepared_copyright_df is now the scraped data
                    prepared_copyright_df = copyright_df_scraped 
                    # Note: If db_copyrights_df was NOT empty, this branch (USCO scrape) wouldn't be hit.
                    # The previous logic for concatenating DB and scraped data is removed as we now EITHER use DB OR scrape.
                    log_message(f"        - Prepared copyrights from scrape. Total: {len(prepared_copyright_df)}.", level="INFO")
                else: # copyright_df_scraped is None or empty after USCO attempt
                    log_message(f"        - No copyright records found on USCO after full scrape.", level='INFO')
            else: # No best_search_params
                log_message(f"        - No suitable copyright records found on USCO after evaluating search combinations.", level='INFO')

        except Exception as e_scrape:
            log_message(f"❌❌ Error during USCO copyright scrape for case index {index}: {e_scrape}", level="ERROR")
            df.at[index, 'validation_status'] = 'error'
        finally:
            if driver is not None:
                driver.quit()
            driver = None # Ensure driver is reset

    # LLM Filtering Step (uses prepared_copyright_df, which is from DB or scrape)
    if prepared_copyright_df is not None and not prepared_copyright_df.empty:
        log_message(f"        - Found {len(prepared_copyright_df)} prepared copyrights (from DB or scrape). Attempting LLM filtering.", level='INFO')
        complaint_pdf_path = find_complaint_pdf_path(case_directory)

        if complaint_pdf_path:
            try:
                prompt_intro = f"You are an expert IP paralegal. Based on the complaint filed with the court, identify the most relevant copyright registrations related to the plaintiff's claim. The plaintiff is: {main_plaintiff_name}.\nThis is the complaint:\n"
                prompt_list_llm = [("text", prompt_intro), ("pdf_path", complaint_pdf_path)]
                
                copyright_details_for_prompt = "\n\nHere is an overview of the copyright registrations found:\n"
                for i, row_series in prepared_copyright_df.iterrows():
                    row = row_series.to_dict()
                    reg_no = row.get("registration_number", "N/A")
                    title = row.get("title", "N/A")
                    # Prefer copyright_claimant, then names
                    claimant_val = row.get("copyright_claimant")
                    names_val = row.get("names")
                    claimant_str = str(claimant_val if pd.notna(claimant_val) and claimant_val else (names_val if pd.notna(names_val) and names_val else "N/A"))
                    
                    reg_date_obj = row.get("registration_date")
                    date_reg_str = reg_date_obj.strftime('%Y-%m-%d') if pd.notna(reg_date_obj) else "N/A"
                    basis_claim = row.get("basis_of_claim", "N/A")
                    type_work = row.get("type_of_work", "N/A")

                    copyright_details_for_prompt += (
                        f"Copyright {i+1}: "
                        f"RegNo: {reg_no}, Title: {title}, Claimant: {claimant_str}, "
                        f"RegDate: {date_reg_str}, Type: {type_work}, Basis: {basis_claim}\n" # Removed Names as it's part of Claimant now
                    )
                
                prompt_instruction = (
                    f"\nBased on the complaint and the copyright list, identify the most relevant copyright registrations (max 10). "
                    "Return ONLY their Registration Numbers as a Python-style list of strings, e.g., "
                    '["VA0001234567", "VA0002345678"]. If none are clearly relevant or no specific works are identifiable, return an empty list [].\n\nRelevant Registration Numbers:'
                )
                prompt_list_llm.append(("text", copyright_details_for_prompt + prompt_instruction))
                
                ai_answer = await vertex_genai_multi_async(prompt_list_llm, model_name=config.get("LLM_MODEL_COPYRIGHT_FILTER", Constants.SMART_MODEL_FREE))
                llm_selected_reg_nos_list = get_list(ai_answer)

                if llm_selected_reg_nos_list:
                    log_message(f"        - LLM selected {len(llm_selected_reg_nos_list)} relevant copyrights: {llm_selected_reg_nos_list}", level='INFO')
                    relevant_copyright_df = prepared_copyright_df[prepared_copyright_df['registration_number'].isin(llm_selected_reg_nos_list)].copy()
                    if relevant_copyright_df.empty:
                        log_message(f"        - LLM selection {llm_selected_reg_nos_list} did not match any prepared copyrights. No specific copyrights for Google Search.", level='WARNING')
                else:
                    log_message(f"        - LLM did not select any specific copyrights. No specific copyrights for Google Search.", level='INFO')
                    relevant_copyright_df = pd.DataFrame()
            except Exception as e_llm:
                log_message(f"        - Error during LLM filtering for copyrights: {e_llm}. Using all prepared copyrights for Google Search.", level='ERROR', exc_info=True)
                relevant_copyright_df = prepared_copyright_df.copy()
        else: # No complaint PDF
            log_message(f"        - No complaint PDF found. LLM filtering skipped. Using all prepared copyrights for Google Search.", level='WARNING')
            relevant_copyright_df = prepared_copyright_df.copy() # Fallback to all prepared if no PDF
    else: # prepared_copyright_df is None or empty
        log_message("        - No prepared copyright data (from DB or scrape) available for LLM filtering. Skipping LLM.", level="WARNING")
        relevant_copyright_df = pd.DataFrame()

    # Add selected/relevant copyrights to IPTrackingManager for Google Search
    reg_nos_for_google_search = []
    if relevant_copyright_df is not None and not relevant_copyright_df.empty:
        log_message(f"        - Adding {len(relevant_copyright_df)} relevant copyrights to IPTrackingManager for Google image search.", level="INFO")
        for _, row_data in relevant_copyright_df.iterrows():
            reg_no = row_data.get('registration_number')
            # Prefer copyright_claimant, then names for artist
            artist_claimant = row_data.get('copyright_claimant')
            artist_names = row_data.get('names')
            artist = str(artist_claimant if pd.notna(artist_claimant) and artist_claimant else (artist_names if pd.notna(artist_names) and artist_names else ''))
            title = str(row_data.get('title', ''))
            ip_manager.add_copyright_to_dataframe(row_data.to_dict(), location_id=f"usco_or_db-{reg_no}")
        
        reg_nos_for_google_search = relevant_copyright_df['registration_number'].tolist()
    
    if reg_nos_for_google_search:
        log_message(f"        - Proceeding to Google Image search for {len(reg_nos_for_google_search)} copyrights.", level="INFO")
        processed_google_count = await get_copyright_images_from_google(
            case_id=case_id,
            ip_manager=ip_manager,
            config=config,
            relevant_reg_numbers_list=reg_nos_for_google_search
        )
    else:
        log_message(f"        - No copyrights selected/available for Google Image search.", level="INFO")

    # !!! Need to update the IPmanager with what was found on google
    
    # Log results to Langfuse
    usco_raw_results_count = 0
    if copyright_df_scraped is not None: # This will be None if we skipped scraping
        usco_raw_results_count = len(copyright_df_scraped)

    langfuse.get_client().update_current_span(
        input={"SearchTerms": list(final_search_terms)},
        output={
            "BestSearchCombination": best_search_params if best_search_params else ("N/A - Scrape Skipped" if (db_copyrights_df is not None and not db_copyrights_df.empty) else "N/A"),
            "USCORawResultsCount": usco_raw_results_count,
            "LLMSelectedCopyrightsCount": len(llm_selected_reg_nos_list) if llm_selected_reg_nos_list else 0,
            "CopyrightsForGoogleSearchCount": len(relevant_copyright_df) if relevant_copyright_df is not None else 0,
            "GoogleImagesProcessedCount": processed_google_count,
            "CopyrightGoalMetAfter": ip_manager.is_goal_met('copyright'),
            "IPTrackingState": ip_manager._state
        }
    )
    # Return True if any copyright data was found by Google images
    return processed_google_count > 0