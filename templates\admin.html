{% extends 'layout.html' %}
{% block content %}
<div class="button-row">
    <div class="button-group">
        <button id="startTask">Run Task Now</button>
        <button id="startUpdateTask">Run Update Task Now</button>
        <button id="refreshApiKeys">Refresh API Keys</button>
        <input type="date" id="taskDate">
        <select id="loopBackDays">
            {% for i in range(1, 21) %}
                <option value="{{ i }}">{{ i }}</option>
            {% endfor %}
        </select>
        <div class="checkbox-wrapper">
            <input type="checkbox" id="forceRedo">
            <label for="forceRedo">Force Redo of cases already fetched</label>
        </div>
    </div>
    <a href="{{ url_for('history') }}" class="button">View Historical Runs</a>
</div>
<div id="progressBar">
    <div class="step" data-step="0">Get Cases</div>
    <div class="step" data-step="1">Process Pictures</div>
    <div class="step" data-step="2">Upload to Database</div>
    <div class="step" data-step="3">Upload Pictures & Files</div>
    <div class="step" data-step="4">AI Tasks</div>
</div>
<div id="logsContainer">
    <div id="logs"></div>
</div>
<script>
    var runId = parseInt("{{ run_id }}", 10);
</script>
{% endblock %}