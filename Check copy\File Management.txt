Client files: 
- App: 
   - Starts on https://tro-1330776830.cos.ap-guangzhou.myqcloud.com/tro-1330776830/2025/01/06/313dff300cbe725038eaaa7788413ed66ea9139385690b40bcf0961743c8bc9a.jpg
   - Get downloaded into a tmp folder (in Do_Check_download.py)
   - Get uploaded to cos (/checks/{check_id}/query/filename.jpg) (in Do_Check_Trademark.py)
      - This does not make sense, we should copy on COS from original location to /checks/{check_id}/query/filename.jpg  (in Do_Check_Download.py)
- API: 
   - Starts on Random URL
   - Get downloaded into a tmp folder (in Do_Check_download.py)
   - Get uploaded to cos (/checks/{check_id}/query/filename.jpg) (in Do_Check_Trademark.py). There is a big cost to this. Alternatrive include:
      - OneDrive
      - Hetzner cold storage

Client part files: 
- Parts are created by Do_Check_Copyright.py
- All picture selected by RAG gets uploaded to cos (/checks/{check_id}/query/filename.jpg) (in Do_Check_Copyright.py).  However:
    - If a check is positive, we should upload to COS for frontend or providing results back to API Client (or a cheaper location)
    - If check is negative, then it is only for langfuse. We should put on the files on local hdd (i.e. the server, because that is the fastest), or onedrive because that is the cheapest
We should also show the original image with the blue quare on it as part of the output of the bounding box function

IP files:
- Currently we download them from COS. !!! They should all be local for speed and bandwidth. It is only 3000 copyright files and 1000 patent files.