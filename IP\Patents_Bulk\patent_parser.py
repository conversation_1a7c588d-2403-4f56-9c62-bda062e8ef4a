# IP/Patents/patent_parser.py

import os
import logging
import multiprocessing
import atexit
import platform
from datetime import datetime
from lxml import etree
import json # For potential future use or debugging

# Assuming Common.cpu_count exists and provides get_allocated_cpus
try:
    from Common.cpu_count import get_allocated_cpus
except ImportError:
    logging.warning("Common.cpu_count not found. Falling back to os.cpu_count().")
    def get_allocated_cpus(percentage=0.75):
        count = os.cpu_count()
        if count is None:
            return 1 # Fallback
        return max(1, int(count * percentage))

# --- Logging Setup ---
log_file_path = os.path.join(os.path.dirname(__file__), '..', '..', 'logs', 'patent_parser.log')
os.makedirs(os.path.dirname(log_file_path), exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(processName)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file_path),
        logging.StreamHandler()
    ]
)

# --- Multiprocessing Pool Management ---
_xml_parser_pool = None

def get_xml_parser_pool():
    """Initializes and returns the global multiprocessing pool."""
    global _xml_parser_pool
    if _xml_parser_pool is None:
        num_cpus = get_allocated_cpus()
        logging.info(f"Initializing multiprocessing pool with {num_cpus} workers.")
        # Use 'spawn' start method for better cross-platform compatibility, especially on Windows/macOS
        start_method = 'spawn' if platform.system() != 'Linux' else None
        context = multiprocessing.get_context(start_method)
        _xml_parser_pool = context.Pool(processes=num_cpus)
        logging.info("Multiprocessing pool initialized.")
    return _xml_parser_pool

def shutdown_xml_parser_pool():
    """Shuts down the global multiprocessing pool."""
    global _xml_parser_pool
    if _xml_parser_pool:
        logging.info("Shutting down multiprocessing pool...")
        _xml_parser_pool.close()
        _xml_parser_pool.join()
        _xml_parser_pool = None
        logging.info("Multiprocessing pool shut down.")

# Register shutdown function to be called on script exit
atexit.register(shutdown_xml_parser_pool)

# --- XML Helper Functions ---
def get_element_text(element, xpath, namespaces=None, default=''):
    """Safely extracts text content from an XML element using XPath."""
    try:
        result = element.xpath(xpath, namespaces=namespaces)
        # Handle cases where xpath might return multiple elements or attributes
        if isinstance(result, list):
            if not result:
                return default
            # If the result is a list of elements, get text from the first one
            # If the result is a list of attribute values (strings), join them (though usually we expect one)
            item = result[0]
            if hasattr(item, 'text'):
                return item.text.strip() if item.text else default
            elif isinstance(item, str):
                return item.strip() # It's an attribute value
            else:
                return default # Unexpected type
        elif isinstance(result, str):
             return result.strip() # Direct attribute value
        elif hasattr(result, 'text'): # Single element result
            return result.text.strip() if result.text else default
        else:
            return default # Handle other unexpected types if necessary
    except Exception as e:
        # Log the error but don't crash the parsing for one missing element
        # logging.debug(f"Error getting text for xpath '{xpath}': {e}")
        return default

def get_element_date(element, xpath, namespaces=None, date_format='%Y%m%d', default=None):
    """Safely extracts and parses a date from an XML element."""
    date_str = get_element_text(element, xpath, namespaces=namespaces)
    if date_str:
        try:
            return datetime.strptime(date_str, date_format).date()
        except ValueError:
            # logging.warning(f"Could not parse date string '{date_str}' with format '{date_format}' for xpath '{xpath}'.")
            return default
    return default

# --- Worker Function ---
def parse_patent_xml_chunk(xml_file_path):
    """
    Parses a single patent XML file (either Grant or CPC) using iterparse.

    Args:
        xml_file_path (str): The path to the XML file.

    Returns:
        tuple: A tuple containing:
               - list: A list of parsed record dictionaries.
               - list: A list of image filenames extracted (only for grant XMLs).
               - str: The input xml_file_path (for tracking).
    """
    records = []
    image_filenames = []
    record_type = None # 'grant' or 'cpc'
    processed_records_in_file = 0

    # Define namespaces commonly found in patent XMLs
    namespaces = {
        'uspat': 'urn:us:gov:doc:uspto:common',
        'pat': 'urn:us:gov:doc:uspto:patent',
        # Add other namespaces as needed based on requirements.md examples
    }

    logging.debug(f"Starting parsing for: {xml_file_path}")

    try:
        context = etree.iterparse(xml_file_path, events=('start', 'end'), recover=True, huge_tree=True)
        event, root = next(context) # Get the root element to determine type

        # Determine XML type from root element tag
        root_tag = etree.QName(root.tag).localname
        if root_tag == 'us-patent-grant':
            record_type = 'grant'
            record_tag_name = 'us-patent-grant'
            logging.debug(f"Detected PTGRDT (Grant) format for {xml_file_path}")
        elif root_tag == 'CPCMasterClassificationRecord':
             # Assuming the namespace prefix might vary, check localname
            record_type = 'cpc'
            record_tag_name = etree.QName(root.tag).localname # Use the actual tag with namespace later if needed
            logging.debug(f"Detected CPCMCPT (CPC) format for {xml_file_path}")
        else:
            logging.warning(f"Unknown root element '{root.tag}' in file {xml_file_path}. Skipping.")
            root.clear() # Clear the root
            # Consume the rest of the iterator to avoid issues if not properly cleared
            while True:
                try:
                    next(context)
                except StopIteration:
                    break
            return [], [], xml_file_path # Return empty results

        # Reset context to parse from the beginning again, focusing on end events of the record tag
        context = etree.iterparse(xml_file_path, events=('end',), tag=root.tag, recover=True, huge_tree=True)

        for event, elem in context:
            if elem.tag == root.tag: # Process the record element
                if record_type == 'grant':
                    record = parse_grant_record(elem, namespaces)
                    if record:
                        records.append(record)
                        # Extract image filenames associated with this grant
                        image_filenames.extend(extract_image_filenames(elem, namespaces))
                        processed_records_in_file += 1
                elif record_type == 'cpc':
                    record = parse_cpc_record(elem, namespaces)
                    if record:
                        records.append(record)
                        processed_records_in_file += 1

                # Efficiently clear memory
                elem.clear()
                # Also clear siblings preceding the element clears
                while elem.getprevious() is not None:
                    del elem.getparent()[0]

        logging.info(f"Successfully parsed {processed_records_in_file} {record_type} record(s) from {os.path.basename(xml_file_path)}")

    except etree.XMLSyntaxError as e:
        logging.error(f"XML Syntax Error parsing {xml_file_path}: {e}")
    except Exception as e:
        logging.error(f"Unexpected error parsing {xml_file_path}: {e}", exc_info=True)

    return records, image_filenames, xml_file_path


def parse_grant_record(elem, namespaces):
    """Parses a <us-patent-grant> element."""
    record = {'record_type': 'grant'}
    bib_data = elem.find('us-bibliographic-data-grant', namespaces)
    if bib_data is None:
        logging.warning("Could not find <us-bibliographic-data-grant> in a grant record.")
        return None # Cannot proceed without bibliographic data

    # Publication Reference
    pub_ref = bib_data.find('publication-reference/document-id', namespaces)
    if pub_ref is not None:
        record['publication_country'] = get_element_text(pub_ref, 'country', namespaces)
        record['publication_doc_number'] = get_element_text(pub_ref, 'doc-number', namespaces)
        record['publication_kind'] = get_element_text(pub_ref, 'kind', namespaces)
        record['publication_date'] = get_element_date(pub_ref, 'date', namespaces)

    # Application Reference
    app_ref = bib_data.find('application-reference', namespaces)
    if app_ref is not None:
        record['application_type'] = app_ref.get('appl-type', '')
        app_ref_doc_id = app_ref.find('document-id', namespaces)
        if app_ref_doc_id is not None:
            record['application_country'] = get_element_text(app_ref_doc_id, 'country', namespaces)
            record['application_doc_number'] = get_element_text(app_ref_doc_id, 'doc-number', namespaces)
            record['application_date'] = get_element_date(app_ref_doc_id, 'date', namespaces)

    # Patent Number (often same as publication doc number, but good to have explicitly if needed)
    record['patent_number'] = record.get('publication_doc_number') # Use publication as primary patent identifier

    # Title
    record['invention_title'] = get_element_text(bib_data, 'invention-title[@id="title"]', namespaces)

    # Classifications
    record['classifications_ipcr'] = [get_element_text(c, 'text()', namespaces) for c in bib_data.xpath('classifications-ipcr/classification-ipcr', namespaces=namespaces)]
    # Assuming Locarno is under 'classification-locarno' based on typical structures
    locarno = bib_data.find('classification-locarno', namespaces)
    if locarno is not None:
         record['classification_locarno_edition'] = get_element_text(locarno, 'edition', namespaces)
         record['classification_locarno_main'] = get_element_text(locarno, 'main-classification', namespaces)

    # National Classifications
    nat_class = bib_data.find('classification-national', namespaces)
    if nat_class is not None:
        record['classification_national_country'] = get_element_text(nat_class, 'country', namespaces)
        record['classification_national_main'] = get_element_text(nat_class, 'main-classification', namespaces)
        record['classification_national_further'] = [get_element_text(fc, 'text()', namespaces) for fc in nat_class.xpath('further-classification', namespaces=namespaces)]

    # Parties
    parties = bib_data.find('parties', namespaces)
    if parties is not None:
        record['applicants'] = [parse_party(p, namespaces) for p in parties.xpath('applicants/applicant', namespaces=namespaces)]
        record['inventors'] = [parse_party(p, namespaces) for p in parties.xpath('inventors/inventor', namespaces=namespaces)]
        record['agents'] = [parse_party(p, namespaces) for p in parties.xpath('agents/agent', namespaces=namespaces)]

    # Examiners
    examiners = bib_data.find('examiners', namespaces)
    if examiners is not None:
        record['primary_examiner'] = get_element_text(examiners, 'primary-examiner/last-name', namespaces) + ", " + get_element_text(examiners, 'primary-examiner/first-name', namespaces)
        record['assistant_examiner'] = get_element_text(examiners, 'assistant-examiner/last-name', namespaces) + ", " + get_element_text(examiners, 'assistant-examiner/first-name', namespaces)


    # Citations (US and Non-Patent Literature)
    citations = bib_data.xpath('references-cited/citation', namespaces=namespaces)
    record['us_citations'] = []
    record['npl_citations'] = []
    for cit in citations:
        patcit = cit.find('patcit', namespaces)
        if patcit is not None:
            doc_id = patcit.find('document-id', namespaces)
            if doc_id is not None:
                record['us_citations'].append({
                    'country': get_element_text(doc_id, 'country', namespaces),
                    'doc_number': get_element_text(doc_id, 'doc-number', namespaces),
                    'kind': get_element_text(doc_id, 'kind', namespaces),
                    'name': get_element_text(doc_id, 'name', namespaces), # Often inventor name
                    'date': get_element_date(doc_id, 'date', namespaces),
                    'category': get_element_text(cit, 'category', namespaces)
                })
        nplcit = cit.find('nplcit', namespaces)
        if nplcit is not None:
            record['npl_citations'].append({
                'text': get_element_text(nplcit, 'text()', namespaces), # Or specific structure if available
                'category': get_element_text(cit, 'category', namespaces)
            })

    # Drawing References (handled by extract_image_filenames)

    # Description
    description_elem = elem.find('description', namespaces)
    if description_elem is not None:
        # Extracting full text might be memory intensive; consider alternatives if needed
        # For now, concatenate paragraphs or take the whole text
        record['description'] = etree.tostring(description_elem, method='text', encoding='unicode').strip()
        # Alternative: Just get top-level text if structure is simple
        # record['description'] = get_element_text(elem, 'description', namespaces)


    # Claims
    claims_elem = elem.find('claims', namespaces)
    if claims_elem is not None:
        record['claims'] = []
        for claim in claims_elem.xpath('claim', namespaces=namespaces):
            claim_text_parts = claim.xpath('.//claim-text//text()', namespaces=namespaces)
            record['claims'].append({
                'id': claim.get('id'),
                'num': claim.get('num'),
                'text': " ".join(part.strip() for part in claim_text_parts if part.strip())
            })

    return record

def parse_party(party_elem, namespaces):
    """Parses an applicant, inventor, or agent element."""
    party_data = {'sequence': party_elem.get('sequence')}
    addressbook = party_elem.find('addressbook', namespaces)
    if addressbook is not None:
        party_data['last_name'] = get_element_text(addressbook, 'last-name', namespaces)
        party_data['first_name'] = get_element_text(addressbook, 'first-name', namespaces)
        party_data['orgname'] = get_element_text(addressbook, 'orgname', namespaces) # For companies
        address = addressbook.find('address', namespaces)
        if address is not None:
            party_data['city'] = get_element_text(address, 'city', namespaces)
            party_data['state'] = get_element_text(address, 'state', namespaces)
            party_data['country'] = get_element_text(address, 'country', namespaces)
            party_data['postcode'] = get_element_text(address, 'postcode', namespaces)
            # Add street etc. if needed
    # Add role for agents if present
    party_data['role'] = get_element_text(party_elem, 'role', namespaces)
    return party_data


def extract_image_filenames(grant_elem, namespaces):
    """Extracts image filenames referenced in a grant record."""
    filenames = []
    # Look for drawing pages or figures containing img tags
    # Adjust the XPath based on the actual structure in requirements.md examples
    for img_elem in grant_elem.xpath('.//drawings//figure//img | .//description//img', namespaces=namespaces):
        fname = img_elem.get('file')
        if fname:
            filenames.append(os.path.basename(fname)) # Store only the filename
    return list(set(filenames)) # Return unique filenames


def parse_cpc_record(elem, namespaces):
    """Parses a <uspat:CPCMasterClassificationRecord> element."""
    # Need to use the actual namespaces defined in the file, or the ones provided
    # Re-check requirements.md for exact structure and namespaces if issues arise.
    # Using provided namespaces dict for now.

    record = {'record_type': 'cpc'}

    # Patent Identification (adjust XPaths based on actual structure)
    # Assuming structure like <pat:PatentIdentification>...</pat:PatentIdentification>
    pat_id = elem.find('.//pat:PatentIdentification', namespaces=namespaces)
    if pat_id is not None:
        record['PatentNumber'] = get_element_text(pat_id, 'pat:PatentNumber', namespaces)
        record['PatentKindCode'] = get_element_text(pat_id, 'pat:PatentKindCode', namespaces)
        record['PatentGrantDate'] = get_element_date(pat_id, 'pat:PatentGrantDate', namespaces)
    else:
        # Try alternative structure if needed, e.g., directly under root
        record['PatentNumber'] = get_element_text(elem, 'pat:PatentNumber', namespaces) # Example fallback
        if not record['PatentNumber']:
             logging.warning(f"Could not find PatentNumber in CPC record.")
             return None # Essential identifier missing

    # Application Identification
    app_id = elem.find('.//pat:ApplicationIdentification', namespaces=namespaces)
    if app_id is not None:
        record['ApplicationNumber'] = get_element_text(app_id, 'pat:ApplicationNumberText', namespaces)
        record['FilingDate'] = get_element_date(app_id, 'pat:FilingDate', namespaces)

    # CPC Classification Bag
    cpc_bag = elem.find('.//pat:CPCClassificationBag', namespaces=namespaces)
    record['cpc_classifications'] = []
    if cpc_bag is not None:
        # Main CPC
        main_cpc = cpc_bag.find('pat:MainCPC', namespaces=namespaces)
        if main_cpc is not None:
            record['cpc_classifications'].append(parse_cpc_classification(main_cpc, 'main', namespaces))

        # Further CPCs
        for further_cpc in cpc_bag.xpath('pat:FurtherCPC', namespaces=namespaces):
            record['cpc_classifications'].append(parse_cpc_classification(further_cpc, 'further', namespaces))

    return record

def parse_cpc_classification(cpc_elem, cpc_type, namespaces):
    """Parses a single CPC classification element (main or further)."""
    classification = {'type': cpc_type}
    # Structure based on typical CPC format, adjust if needed from requirements.md
    classification['section'] = get_element_text(cpc_elem, 'pat:Section', namespaces)
    classification['class'] = get_element_text(cpc_elem, 'pat:Class', namespaces)
    classification['subclass'] = get_element_text(cpc_elem, 'pat:Subclass', namespaces)
    classification['main_group'] = get_element_text(cpc_elem, 'pat:MainGroup', namespaces)
    classification['subgroup'] = get_element_text(cpc_elem, 'pat:Subgroup', namespaces)
    classification['symbol'] = get_element_text(cpc_elem, 'pat:SymbolPosition', namespaces) # Or another element for full symbol?
    classification['value'] = get_element_text(cpc_elem, 'pat:ClassificationValue', namespaces)
    classification['version_date'] = get_element_date(cpc_elem, 'pat:ClassificationVersionDate', namespaces)
    classification['data_source'] = get_element_text(cpc_elem, 'pat:ClassificationDataSource', namespaces)
    classification['generating_office'] = get_element_text(cpc_elem, 'pat:GeneratingOfficeCountryCode', namespaces)
    # Construct full CPC symbol if not directly available
    classification['full_symbol'] = f"{classification['section']}{classification['class']}{classification['subclass']}{classification['main_group']}/{classification['subgroup']}"

    return classification


# --- Orchestrator Function ---
def parse_patent_xml_parallel(xml_file_paths):
    """
    Parses multiple patent XML files in parallel using a multiprocessing pool.

    Args:
        xml_file_paths (list): A list of paths to patent XML files.

    Returns:
        tuple: A tuple containing:
               - list: An aggregated list of all parsed record dictionaries.
               - list: An aggregated list of all unique image filenames.
    """
    if not xml_file_paths:
        logging.info("No XML file paths provided for parsing.")
        return [], []

    pool = get_xml_parser_pool()
    all_records = []
    all_image_filenames = set() # Use a set for automatic deduplication

    logging.info(f"Starting parallel parsing for {len(xml_file_paths)} XML files.")

    results = pool.imap_unordered(parse_patent_xml_chunk, xml_file_paths)

    processed_files = 0
    total_files = len(xml_file_paths)
    for records, image_filenames, processed_path in results:
        processed_files += 1
        if records:
            all_records.extend(records)
        if image_filenames:
            all_image_filenames.update(image_filenames)
        logging.debug(f"Completed processing for {os.path.basename(processed_path)} ({processed_files}/{total_files})")

    logging.info(f"Finished parallel parsing. Aggregated {len(all_records)} records and {len(all_image_filenames)} unique image filenames.")

    return all_records, list(all_image_filenames)

# --- Example Usage (Optional) ---
if __name__ == '__main__':
    logging.info("Running patent_parser.py as main script (Example Usage)")

    # Create dummy XML files for testing if they don't exist
    dummy_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'data', 'patents', 'dummy')
    os.makedirs(dummy_dir, exist_ok=True)
    dummy_grant_xml = os.path.join(dummy_dir, 'dummy_grant.xml')
    dummy_cpc_xml = os.path.join(dummy_dir, 'dummy_cpc.xml')

    # Basic Grant XML structure
    grant_xml_content = """<?xml version="1.0" encoding="UTF-8"?>
    <!DOCTYPE us-patent-grant SYSTEM "us-patent-grant-v46-2022-11-15.dtd" [ ]>
    <us-patent-grant lang="EN" dtd-version="v4.6 2022-11-15" file="US11234567-20230101.XML" status="PRODUCTION" id="us-patent-grant" country="US" date-produced="20221220" date-publ="20230101">
    <us-bibliographic-data-grant>
    <publication-reference>
    <document-id>
    <country>US</country>
    <doc-number>11234567</doc-number>
    <kind>B2</kind>
    <date>20230101</date>
    </document-id>
    </publication-reference>
    <application-reference appl-type="utility">
    <document-id>
    <country>US</country>
    <doc-number>16000001</doc-number>
    <date>20200115</date>
    </document-id>
    </application-reference>
    <us-application-series-code>16</us-application-series-code>
    <invention-title id="title">Example Patent Title</invention-title>
    <parties>
        <applicants><applicant sequence="001" app-type="applicant-inventor"><addressbook><last-name>Doe</last-name><first-name>John</first-name><address><city>Anytown</city><country>US</country></address></addressbook></applicant></applicants>
        <inventors><inventor sequence="001" designation="us-only"><addressbook><last-name>Doe</last-name><first-name>John</first-name><address><city>Anytown</city><country>US</country></address></addressbook></inventor></inventors>
    </parties>
    <examiners><primary-examiner><last-name>Smith</last-name><first-name>Jane</first-name></primary-examiner></examiners>
    <references-cited><citation><patcit num="0001"><document-id><country>US</country><doc-number>9876543</doc-number><kind>B1</kind><date>20190505</date></document-id></patcit><category>cited by examiner</category></citation></references-cited>
    </us-bibliographic-data-grant>
    <description id="description">
        <heading id="h-0001">BACKGROUND</heading>
        <p id="p-0001">This is the description.</p>
        <figure id="fig-0001"><img id="EMI-0001" file="USD11234567-20230101-D00000.TIF" alt="Figure 1" he="100" wi="100"/></figure>
    </description>
    <claims id="claims">
    <claim id="claim-1" num="00001">
    <claim-text>1. A claimed invention comprising: an element.</claim-text>
    </claim>
    </claims>
    <drawings id="DRAWINGS">
        <figure id="fig-D00001" num="00001">
            <img id="EMI-D00001" file="USD11234567-20230101-D00001.TIF" alt="drawing" he="123.45mm" wi="123.45mm"/>
        </figure>
    </drawings>
    </us-patent-grant>
    """
    if not os.path.exists(dummy_grant_xml):
        with open(dummy_grant_xml, 'w', encoding='utf-8') as f:
            f.write(grant_xml_content)
        logging.info(f"Created dummy grant XML: {dummy_grant_xml}")

    # Basic CPC XML structure (simplified, adjust namespaces/structure as needed)
    cpc_xml_content = """<?xml version="1.0" encoding="UTF-8"?>
    <uspat:CPCMasterClassificationRecord xmlns:uspat="urn:us:gov:doc:uspto:common" xmlns:pat="urn:us:gov:doc:uspto:patent">
      <pat:PatentIdentification>
        <pat:PatentNumber>11000001</pat:PatentNumber>
        <pat:PatentKindCode>B2</pat:PatentKindCode>
        <pat:PatentGrantDate>20221231</pat:PatentGrantDate>
      </pat:PatentIdentification>
      <pat:ApplicationIdentification>
         <pat:ApplicationNumberText>15999999</pat:ApplicationNumberText>
         <pat:FilingDate>20190101</pat:FilingDate>
      </pat:ApplicationIdentification>
      <pat:CPCClassificationBag>
        <pat:MainCPC>
          <pat:Section>G</pat:Section>
          <pat:Class>06</pat:Class>
          <pat:Subclass>F</pat:Subclass>
          <pat:MainGroup>16</pat:MainGroup>
          <pat:Subgroup>2457</pat:Subgroup>
          <pat:SymbolPosition>L</pat:SymbolPosition>
          <pat:ClassificationValue>I</pat:ClassificationValue>
          <pat:ClassificationVersionDate>20210101</pat:ClassificationVersionDate>
          <pat:ClassificationDataSource>H</pat:ClassificationDataSource>
          <pat:GeneratingOfficeCountryCode>US</pat:GeneratingOfficeCountryCode>
        </pat:MainCPC>
        <pat:FurtherCPC>
          <pat:Section>H</pat:Section>
          <pat:Class>04</pat:Class>
          <pat:Subclass>L</pat:Subclass>
          <pat:MainGroup>9</pat:MainGroup>
          <pat:Subgroup>08</pat:Subgroup>
          <pat:SymbolPosition>F</pat:SymbolPosition>
          <pat:ClassificationValue>I</pat:ClassificationValue>
          <pat:ClassificationVersionDate>20210101</pat:ClassificationVersionDate>
          <pat:ClassificationDataSource>H</pat:ClassificationDataSource>
          <pat:GeneratingOfficeCountryCode>US</pat:GeneratingOfficeCountryCode>
        </pat:FurtherCPC>
      </pat:CPCClassificationBag>
    </uspat:CPCMasterClassificationRecord>
    """
    if not os.path.exists(dummy_cpc_xml):
         with open(dummy_cpc_xml, 'w', encoding='utf-8') as f:
            f.write(cpc_xml_content)
         logging.info(f"Created dummy CPC XML: {dummy_cpc_xml}")

    # --- Test Execution ---
    test_files = [dummy_grant_xml, dummy_cpc_xml]
    if all(os.path.exists(f) for f in test_files):
        logging.info(f"Parsing test files: {test_files}")
        all_parsed_records, all_parsed_images = parse_patent_xml_parallel(test_files)

        logging.info(f"--- Example Parsed Records ({len(all_parsed_records)}) ---")
        # Print limited number of records for brevity
        for i, record in enumerate(all_parsed_records[:2]):
             print(f"Record {i+1}:")
             # Pretty print the dictionary
             import pprint
             pprint.pprint(record)
             print("-" * 20)

        logging.info(f"--- Example Image Filenames ({len(all_parsed_images)}) ---")
        print(all_parsed_images[:10]) # Print first 10 images

        # Ensure pool is shut down if run as script (atexit handles this, but good practice)
        # shutdown_xml_parser_pool() # atexit handles this automatically
    else:
        logging.warning("Could not find dummy XML files for example execution.")

    logging.info("patent_parser.py example finished.")