import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import pandas as pd
from Alerts.zz_Get_Alerts import connect_to_nas
from DatabaseManagement.ImportExport import get_table_from_GZ
from Common.Constants import nas_case_folder, local_case_folder, nas_assess_folder, local_assess_folder, sanitize_name
from FileManagement.SendToNAS import sftp_exists, transfer_local_to_nas, transfer_nas_to_local
from Alerts.PicturesProcessing.ExtractPictures import extract_images_from_a_pdf
import shutil
from Alerts.PicturesProcessing.OCRProcessor import OCRProcessor
import fitz
import pytesseract
import cv2
import numpy as np
from multiprocessing import Pool, cpu_count
from tqdm import tqdm
from io import StringIO
# Removed: import logging
from logdata import log_message # Added logdata import

# No progress bar
# def extract_evidence_pictures_for_cases_multiprocess(df_cases, df_steps, df_plaintiff):
#     num_processes = min(cpu_count(), len(df_cases))
#     with Pool(processes=num_processes, initializer=init_worker) as pool:
#         args = [(case_row, df_steps, df_plaintiff) for _, case_row in df_cases.iterrows()]
#         results = pool.starmap(extract_evidence_pictures_for_case_multiprocess, args)


# required to make tqmd progress work
def multiprocess_case(args):
    # Redirect stdout for the subprocess, otherwise we cannot see the progress bar
    old_stdout = sys.stdout
    sys.stdout = StringIO()
    # Removed: logging.getLogger("paramiko").setLevel(logging.WARNING) # Control external logs differently if needed

    try:
        result = extract_evidence_pictures_for_case_multiprocess(*args)
        return result
    finally:
        # Restore stdout
        sys.stdout = old_stdout

def extract_evidence_pictures_for_cases_multiprocess(df_cases, df_steps, df_plaintiff):
    num_processes = min(cpu_count(), len(df_cases))

    log_message(f"Starting processing of {len(df_cases)} cases with {num_processes} processes...", level='INFO')

    with Pool(processes=num_processes, initializer=OCRProcessor.init_worker) as pool:
        args = [(case_row, df_steps, df_plaintiff) for _, case_row in df_cases.iterrows()]

        with tqdm(total=len(df_cases), desc="Processing cases", unit="case") as pbar:
            for _ in pool.imap_unordered(multiprocess_case, args):
                pbar.update()



def extract_evidence_pictures_for_case_multiprocess(case_row, df_steps, df_plaintiff):
    sftp, transport = connect_to_nas()
    extract_evidence_pictures_for_case(case_row, df_steps, df_plaintiff, sftp)
    sftp.close()
    transport.close()


def extract_evidence_pictures_for_cases(df_cases, df_steps, df_plaintiff, sftp):
    for index, case_row in df_cases.iterrows():
        extract_evidence_pictures_for_case(case_row, df_steps, df_plaintiff, sftp)

# @profile
def extract_evidence_pictures_for_case(case_row, df_steps, df_plaintiff, sftp):
    log_message(f"Processing case {case_row['date_filed']} - {case_row['docket']}", level='INFO')
    df_steps_for_case = df_steps[df_steps['case_idddd'] == case_row['id']]

    max_links = 3
    evidence_row = None
    for index, row in df_steps_for_case.iterrows():
        nblinks_row = row["files_number"] if not pd.isna(row["files_number"]) else 0
        td_text = row["proceeding_text"].lower()
        if nblinks_row > max_links and any(keyword in td_text for keyword in ["declaration", "tro", "temporary restraining order"]) and not "declaration of keith" in td_text and int(row['step_nb']) != 1:
            max_links = nblinks_row
            evidence_row = row

    if evidence_row is not None:
        log_message(f"     - Evidence row found for case {case_row['date_filed']} - {case_row['docket']}", level='DEBUG') # Changed to DEBUG
        row = evidence_row
        step_nb = row['step_nb']

        date_filed = case_row['date_filed']

        nas_this_case_folder = nas_case_folder + "/" + sanitize_name(f"{pd.to_datetime(date_filed, errors='coerce').strftime("%Y-%m-%d")} - {case_row['docket']}")
        nas_case_step_folder = nas_this_case_folder + "/" + str(int(step_nb))
        local_this_case_folder = os.path.join(local_case_folder, sanitize_name(f"{pd.to_datetime(date_filed, errors='coerce').strftime("%Y-%m-%d")} - {case_row['docket']}"))
        local_case_step_folder = os.path.join(local_this_case_folder, str(int(step_nb)))
        if not os.path.exists(local_case_step_folder) and sftp_exists(sftp, nas_case_step_folder):
            transfer_nas_to_local(sftp, nas_this_case_folder, local_this_case_folder)
        elif not os.path.exists(local_case_step_folder):
            return None

        # Extract all the images of all the pdfs for that step
        for pdf_file in os.listdir(local_case_step_folder): # exhibit but not "Seizure Statistics", not "certificate of registration", not "Notorious markets", "INTA" in first 3 pages
            if pdf_file.endswith(".pdf"):
                pdf_document = fitz.open(os.path.join(local_case_step_folder, pdf_file))
                ocr_text1, ocr_data1, img_path1 = OCRProcessor.process_single_page(pdf_document, 0)
                ocr_text2, ocr_data2, img_path2 = OCRProcessor.process_single_page(pdf_document, 1)
                ocr_text3, ocr_data3, img_path3 = OCRProcessor.process_single_page(pdf_document, 2)
                pdf_document.close()
                first_3_pages = ocr_text1.lower() + " " + ocr_text2.lower() + " " + ocr_text3.lower()
                if not any(keyword in first_3_pages for keyword in ["22-cv-01321", "heavy recruitment of chinese", "counterfeit and pirated goods", "office of strategy", "counterfeiting in the age of the internet", "accreditation", "department of commerce", "white paper", "chamber of commerce", "office of trade", "border protection", "hague convention", "civil procedure", "convention on the service", "seizure statistics", "certificate of registration", "notorious markets", "inta", "traffic report", "principal register", "silk road", "state of the", "briefing papers", "united states patent", "united states design", "patent no"]):
                    log_message(f"         - Extracting images from {pdf_file}", level='DEBUG') # Changed to DEBUG
                    extract_images_from_a_pdf(local_case_step_folder, pdf_file, 5)
                    delete_if_bad_aspect_ratio(os.path.join(local_case_step_folder, os.path.splitext(pdf_file)[0]))
                    delete_black_images(os.path.join(local_case_step_folder, os.path.splitext(pdf_file)[0]))
                    delete_if_no_color(os.path.join(local_case_step_folder, os.path.splitext(pdf_file)[0]))


        # Create the folder for the plaintiff's evidence
        sanitized_plaintiff_name = sanitize_name(df_plaintiff[df_plaintiff['id'] == case_row['plaintiff_id']]['plaintiff_name'].values[0])
        plaintiff_evidence_folder = os.path.join(local_assess_folder, case_row['nos_description'], sanitized_plaintiff_name)

        # Copy all the evidence images to the plaintiff's evidence folder
        for pdf_folder in os.listdir(local_case_step_folder):
            if os.path.isdir(os.path.join(local_case_step_folder, pdf_folder)):
                os.makedirs(plaintiff_evidence_folder, exist_ok=True)

                for image_file in os.listdir(os.path.join(local_case_step_folder, pdf_folder)):
                    shutil.copy(os.path.join(local_case_step_folder, pdf_folder, image_file), plaintiff_evidence_folder)


def delete_if_bad_aspect_ratio(pdf_path):
    if os.path.exists(pdf_path):
        for image_file in os.listdir(pdf_path):
            image_path = os.path.join(pdf_path, image_file)
            img = cv2.imread(image_path)
            if img.shape[1] > 2.5 * img.shape[0]:
                shutil.move(image_path, os.path.join(local_assess_folder, "bad_aspect_ratio"))

def delete_black_images(pdf_path):
    if os.path.exists(pdf_path):
        for image_file in os.listdir(pdf_path):
            image_path = os.path.join(pdf_path, image_file)
            with open(image_path, 'rb') as img_file:
                img_file.seek(0)
                file_data = img_file.read()
                if file_data.count(b'\x00') / len(file_data) >= 0.995:
                    shutil.move(image_path, os.path.join(local_assess_folder, "black_images"))

def delete_if_no_color(pdf_path):
    if os.path.exists(pdf_path):
        for image_file in os.listdir(pdf_path):
            image_path = os.path.join(pdf_path, image_file)
            try:
                img = cv2.imread(image_path)
                if img is None:
                    continue  # Skip if the file is not an image
                # Check if the image is grayscale (single channel)
                if len(img.shape) < 3 or img.shape[2] == 1:
                    shutil.move(image_path, os.path.join(local_assess_folder, "no_color"))
                    continue
                # Efficiently check if all color channels are equal
                min_channel = np.min(img, axis=2)
                max_channel = np.max(img, axis=2)
                if np.all(min_channel == max_channel):
                    shutil.move(image_path, os.path.join(local_assess_folder, "no_color"))
            except Exception as e:
                log_message(f"Error processing {image_file}: {e}", level='ERROR')


if __name__ == "__main__":
    if os.name == "nt":
        pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
    else:
        pytesseract.pytesseract.tesseract_cmd = 'tesseract'

    sftp, transport = connect_to_nas()
    df_db = get_table_from_GZ("tb_case")
    df_steps = get_table_from_GZ("tb_case_steps")
    df_plaintiff = get_table_from_GZ("tb_plaintiff")

    # Clear the anomalies folders
    os.makedirs(os.path.join(local_assess_folder, "no_color"), exist_ok=True)
    os.makedirs(os.path.join(local_assess_folder, "black_images"), exist_ok=True)
    os.makedirs(os.path.join(local_assess_folder, "bad_aspect_ratio"), exist_ok=True)
    for folder in [os.path.join(local_assess_folder, "no_color"), os.path.join(local_assess_folder, "black_images"), os.path.join(local_assess_folder, "bad_aspect_ratio")]:
        for file in os.listdir(folder):
            os.remove(os.path.join(folder, file))

    from datetime import datetime
    date_start = "2024-01-02"
    date_end = "2024-02-29"
    date_start = datetime.strptime(date_start, "%Y-%m-%d").date()
    date_end = datetime.strptime(date_end, "%Y-%m-%d").date()

    # df_db['date_filed'] = pd.to_datetime(df_db['date_filed']).dt.date
    df_db = df_db[df_db['date_filed'] >= date_start]
    df_db = df_db[df_db['date_filed'] <= date_end]
    # df_db = df_db[df_db['docket'].str.contains("20079", na=False)]
    df_db = df_db[df_db['nos_description'].str.contains("Trademark", na=False)]
    # extract_evidence_pictures_for_cases(df_db, df_steps, df_plaintiff, sftp)
    extract_evidence_pictures_for_cases_multiprocess(df_db, df_steps, df_plaintiff)
    sftp.close()
    transport.close()
