#nofloqa
import asyncio, json, aiohttp, os, sys, shutil, re
import json
import traceback
from collections import defaultdict
sys.path.append(os.getcwd())
import Common.Constants as Constants
from langfuse import observe
import langfuse
from typing import List, Dict
from AI.GCV_GetImageParts import get_image_parts_async # Added for splitting multi-images
from AI.GC_VertexAI import vertex_genai_multi_async # Added for image classification
from PIL import Image # Added for image conversion
from logdata import log_message
from Scraper.ChineseWebsites_A_find_urls import A_find_target_urls_using_markdown
from Scraper.ChineseWebsites_B_extract_from_urls import B_extract_data_from_url
from Scraper.ChineseWebsites_C_copyright import C_copyright_process_picture
from Scraper.ChineseWebsites_C_copyright_image_deduplicator import deduplicate_images, deduplicate_selected_images, calculate_colorfulness
from Scraper.ChineseWebsites_D_map_reg_nos_to_images import D_map_reg_nos_to_images_async
from Scraper.ChineseWebsites_utils import WEBSITE_CONFIG

from IP.Copyrights.Copyright_USCO import extract_formated_copyright_registration_number, get_info_from_USCO_using_reg_no
from Alerts.Chrome_Driver import get_driver
from Alerts.IPTrackingManager import IPTrackingManager
from AI.LLM_shared import get_list
import pandas as pd

# ❌⚠️📥🔥✅☑️✔️💯💧⛏🔨🗝🔑 🔒💡⛔🚫❗

@observe()
async def scrape_case_data(cases_df: pd.DataFrame, date_filed: str, docket: str, plaintiff_name: str, required_ip: List[str], case_images_directory: str, ip_manager: IPTrackingManager) -> Dict:
    """Scrape IP-related data for a given case docket across multiple sources."""

    # --- DIRECTORY SETUP AND CLEANUP ---
    case_folder = Constants.sanitize_name(f"{date_filed.strftime('%Y-%m-%d')} - {docket}")
    copyright_case_base_dir = os.path.join(Constants.local_ip_folder, "Copyrights", "Case Files", case_folder)
    if os.path.exists(copyright_case_base_dir): shutil.rmtree(copyright_case_base_dir)
    os.makedirs(copyright_case_base_dir, exist_ok=True) # Ensure base directory exists
    log_message(f"   Preparing copyright directory: {copyright_case_base_dir}")
    
    copyright_cn_allpictures_dir = os.path.join(copyright_case_base_dir, "copyright_cn_allpictures")
    os.makedirs(copyright_cn_allpictures_dir, exist_ok=True)
    multi_originals_dir = os.path.join(copyright_case_base_dir, "multi_originals")
    os.makedirs(multi_originals_dir, exist_ok=True)
    watermarked_dir = os.path.join(copyright_case_base_dir, "copyright_cn_selected_watermarked")
    os.makedirs(watermarked_dir, exist_ok=True)
    reverse_search_output_dir = os.path.join(copyright_case_base_dir, "reverse_search_results")
    os.makedirs(reverse_search_output_dir, exist_ok=True)
    reverse_search_final_dir = os.path.join(copyright_case_base_dir, "reverse_search_final")
    os.makedirs(reverse_search_final_dir, exist_ok=True)
    duplicate_dir_C_step = os.path.join(copyright_case_base_dir, "duplicate_images_C_step")
    os.makedirs(duplicate_dir_C_step, exist_ok=True)
    log_message(f"   Cleaned and created necessary subdirectories for the current run.")
    # --- END SECTION FOR DIRECTORY SETUP AND CLEANUP ---


    # These will store the final extracted IP data
    final_trademarks_set, final_patents_set, final_artist_urls_set = set(), set(), set()
    master_reg_nos_for_usco_fetch = set() # Collect all unique standardized reg_nos for USCO fetching
    
    ### --- Stage A : search for the URLs --- ###
    async with aiohttp.ClientSession() as session:
        # Discover URLs to crawl from each configured website - using site-specific format
        url_results = await asyncio.gather(*[A_find_target_urls_using_markdown(docket, plaintiff_name, session, config) for config in WEBSITE_CONFIG])
        if sum(len(urls) for urls in url_results) == 0:
            list_of_other_cases = cases_df[cases_df["plaintiff_id"] == ip_manager.plaintiff_id]["docket"].tolist() # Get list of other cases with the same plaintiff
            for other_case in list_of_other_cases:
                if other_case != docket:
                    log_message(f"   No URLs found for {docket}. Checking other cases with the same plaintiff: {other_case}")
                    url_results = await asyncio.gather(*[A_find_target_urls_using_markdown(other_case, plaintiff_name, session, config) for config in WEBSITE_CONFIG])
                    if sum(len(urls) for urls in url_results) > 0:
                        break

        # Extract data for unique URLs
        processed_urls = set()
        extraction_tasks = []



     ### --- Stage B : extract patent / trademark reg no, and copyright pictures from the URLs --- ###
        for i, config in enumerate(WEBSITE_CONFIG):
            for url in url_results[i]:
                if url not in processed_urls:
                    extraction_tasks.append(B_extract_data_from_url(session, url, config["name"], docket, plaintiff_name, date_filed, case_folder, copyright_cn_allpictures_dir))
                    processed_urls.add(url)

        log_message(f"   Scheduled {len(extraction_tasks)} unique URLs for data extraction.")
        results = await asyncio.gather(*extraction_tasks)



    ### --- Stage 1: Map identifiers to their associated copyright items and collect other IP types --- ###
    identifier_to_copyright_items_map: Dict[str, List[Dict]] = {}
    
    for data_from_B, img_map_from_B in results: # img_map_from_B is {identifier: (temp_persistent_path, original_image_url)}
        if not isinstance(data_from_B, dict):
            continue

        # Accumulate trademarks, patents, and artist_urls
        final_trademarks_set.update(data_from_B.get("trademarks", []) if "trademark" in required_ip else [])
        final_patents_set.update(data_from_B.get("patents", []) if "patent" in required_ip else [])
        if artist_url := data_from_B.get("artist_url"):
            final_artist_urls_set.add(artist_url)

        if "copyright" not in required_ip:
            continue

        source_page_url_from_B = data_from_B.get("source_page_url", "Unknown")
        source_site_from_B = data_from_B.get("source_site", "Unknown")
        watermark_desc_from_B = next((s["watermark_description"] for s in WEBSITE_CONFIG if s["name"] == source_site_from_B), "the watermark")

        for item_b in data_from_B.get("copyrights", []): # item_b is {"reg_no": "...", "identifier": "..."}
            original_reg_no_from_item_b = item_b.get("reg_no")
            identifier_from_item_b = item_b.get("identifier")

            # Attempt to get a strictly formatted USCO registration number for fetching
            fetchable_usco_reg_no = extract_formated_copyright_registration_number(original_reg_no_from_item_b)
            if fetchable_usco_reg_no:
                master_reg_nos_for_usco_fetch.add(fetchable_usco_reg_no)

            # For map and C-step, standardized_reg_no can include placeholders ("no_reg_X", "multi")
            current_item_standardized_reg_no = fetchable_usco_reg_no # Start with fetchable
            if not current_item_standardized_reg_no and original_reg_no_from_item_b and \
               original_reg_no_from_item_b.lower().startswith(("no_reg_", "multi")):
                current_item_standardized_reg_no = original_reg_no_from_item_b # Keep placeholder

            if not current_item_standardized_reg_no: # If reg_no is unusable even as a placeholder
                log_message(f"      Warning: Skipping copyright item due to unresolvable/missing reg_no: {item_b} (original: {original_reg_no_from_item_b}) from {source_page_url_from_B}")
                continue # Skip this item_b entirely

            # If there's an identifier, try to map it to an image
            if identifier_from_item_b and isinstance(identifier_from_item_b, str):
                img_path_in_allpictures, original_img_url_from_B_map = img_map_from_B.get(identifier_from_item_b, (None, None))
                if not img_path_in_allpictures or not os.path.exists(img_path_in_allpictures):
                    log_message(f"      Warning: Image path for identifier '{identifier_from_item_b}' not found or file missing: {img_path_in_allpictures}. Skipping item for map.")
                    continue # Skip adding to map, but fetchable_usco_reg_no might have been added

                if identifier_from_item_b not in identifier_to_copyright_items_map:
                    copyright_item_details = {
                        "original_reg_no": original_reg_no_from_item_b,
                        "standardized_reg_no": current_item_standardized_reg_no,
                        "identifier": identifier_from_item_b,
                        "img_path_in_allpictures": img_path_in_allpictures,
                        "original_img_url": original_img_url_from_B_map,
                        "source_page_url": source_page_url_from_B,
                        "source_site": source_site_from_B,
                        "watermark_description": watermark_desc_from_B
                    }
                if identifier_from_item_b not in identifier_to_copyright_items_map:
                    identifier_to_copyright_items_map[identifier_from_item_b] = []
                identifier_to_copyright_items_map[identifier_from_item_b].append(copyright_item_details)
            # If no identifier, fetchable_usco_reg_no might have been added. Nothing more for this item_b.



    # --- Stage 2: Process Identifiers, AI Classification for when we have multiple reg numbers pointing to the same image ---
    images_for_C_processing = [] # List to hold dicts for C_copyright_process_picture
    processed_multi_image_paths = set() # Avoid re-splitting the same multi-image
    multi_index_counter = 1
    no_reg_index_counter = 1

    for identifier, items_for_identifier in identifier_to_copyright_items_map.items():
        if not items_for_identifier: continue

        first_item = items_for_identifier[0]
        img_path = first_item["img_path_in_allpictures"]
        original_img_url = first_item["original_img_url"]
        source_page_url = first_item["source_page_url"]
        source_site = first_item["source_site"]
        watermark_desc = first_item["watermark_description"]

        current_std_reg_nos_for_identifier = {item["standardized_reg_no"] for item in items_for_identifier if item["standardized_reg_no"]}
        
        image_nature = "single-artwork" # Default
        # Check if any original reg_no was "multi" or if multiple items point to this identifier
        is_explicitly_multi = any(item["original_reg_no"] and item["original_reg_no"].lower().startswith("multi") for item in items_for_identifier)

        # Multiple reg map to the same picture
        if len(items_for_identifier) > 1:
            if not os.path.exists(img_path):
                log_message(f"      Error: Image path {img_path} for identifier {identifier} does not exist. Cannot classify with AI. Skipping.", level="ERROR")
                continue
            prompt_img_classify = f"This image ({identifier}) is associated with multiple copyright registration numbers or was marked as a multi-image. Is this image a 'multi-image' (contains multiple distinct copyrightable artworks), a 'table' (contains textual information like a list of registration numbers but does not contain any actual artwork), or a 'single-artwork' (a single piece of art, possibly with variations)? Respond with only 'multi-image', 'table', or 'single-artwork'."
            ai_classification_response = await vertex_genai_multi_async([("text", prompt_img_classify), ("image_path", img_path)], model_name=Constants.IMAGE_MODEL_FREE_LIMITED, useVertexAI=Constants.IMAGE_MODEL_FREE_LIMITED_VERTEX)
            image_nature = ai_classification_response.strip().lower() if ai_classification_response else "single-artwork"
            log_message(f"      AI classification for image {identifier} ({img_path.replace(os.getcwd(), '')}): {image_nature}")

        if image_nature == "table":
            log_message(f"      Image {identifier} classified as 'table'. Associated reg_nos: {current_std_reg_nos_for_identifier} will be fetched from USCO but image not processed by C_copyright.")
            continue 

        elif image_nature == "multi-image" or is_explicitly_multi:
            if img_path in processed_multi_image_paths:
                log_message(f"      Multi-image {img_path.replace(os.getcwd(), '')} already processed for splitting. Skipping.")
                continue
            processed_multi_image_paths.add(img_path)
            log_message(f"      Splitting multi-image: {img_path.replace(os.getcwd(), '')}")
            
            multi_original_filename = f"original_multi_{multi_index_counter}_{os.path.basename(img_path)}"
            multi_original_dest_path = os.path.join(multi_originals_dir, multi_original_filename)
            if os.path.exists(img_path): # Ensure file exists before moving
                shutil.move(img_path, multi_original_dest_path)
                # log_message(f"        Moved original multi-image to {multi_original_dest_path}")
            else:
                log_message(f"        Error: Source multi-image {img_path} not found for move.", level="ERROR")
                continue # Skip if original multi-image is missing

            try:
                image_parts_with_label = await get_image_parts_async('Identify individual images that might be copyrighted. Provide the copyright registration number (usually 2 letters followed by numbers) as the label', multi_original_dest_path, model_name=Constants.IMAGE_MODEL_FREE_LIMITED, useVertexAI=Constants.IMAGE_MODEL_FREE_LIMITED_VERTEX)
                if not image_parts_with_label:
                    log_message(f"        Warning: No parts extracted from multi-image {multi_original_dest_path}.", level="WARNING")
                else:
                    for part_idx, part_info in enumerate(image_parts_with_label):
                        part_path_temp = part_info["path"]
                        part_base, part_ext = os.path.splitext(os.path.basename(part_path_temp))
                        part_filename_in_allpictures = f"multi_{multi_index_counter}_part_{part_idx + 1}{part_ext}"
                        part_dest_path_in_allpictures = os.path.join(copyright_cn_allpictures_dir, part_filename_in_allpictures)
                        shutil.copy(part_path_temp, part_dest_path_in_allpictures)
                        # log_message(f"          Copied split part to {part_dest_path_in_allpictures}")

                        if len(re.findall(r"\d", part_info["label"])) >= 5 and extract_formated_copyright_registration_number(part_info["label"]):
                            reg_no_to_save = extract_formated_copyright_registration_number(part_info["label"])
                        else:
                            reg_no_to_save = f"multi_{multi_index_counter}_part_{part_idx + 1}"
                            
                        images_for_C_processing.append({
                            "reg_no": reg_no_to_save,
                            "img_url": original_img_url,
                            "download_path": part_dest_path_in_allpictures,
                            "source_page_url": source_page_url,
                            "source_site": source_site,
                            "watermark_description": watermark_desc,
                            "candidate_usco_reg_nos": list(r for r in current_std_reg_nos_for_identifier if r and not r.lower().startswith(("no_reg_", "multi")))
                        })
            except Exception as e_split:
                log_message(f"        Error splitting multi-image {multi_original_dest_path}: {e_split}", level="ERROR")
            multi_index_counter += 1
        
        elif image_nature == "single-artwork": # Also handles the case where len(items_for_identifier) == 1 implicitly
            primary_reg_no_for_c = None
            # Determine the primary reg_no for this single artwork
            valid_std_reg_nos = [item["standardized_reg_no"] for item in items_for_identifier if item["standardized_reg_no"] and not item["standardized_reg_no"].lower().startswith(("no_reg_", "multi"))]
            no_reg_placeholders = [item["standardized_reg_no"] for item in items_for_identifier if item["standardized_reg_no"] and item["standardized_reg_no"].lower().startswith("no_reg_")]

            if valid_std_reg_nos:
                primary_reg_no_for_c = valid_std_reg_nos[0]
            elif no_reg_placeholders: # If only "no_reg_" placeholders were found by B_extract or standardized to it
                primary_reg_no_for_c = f"no_reg_{no_reg_index_counter}"
                no_reg_index_counter += 1
            elif items_for_identifier: # Fallback if all original reg_nos were empty or unstandardizable but not placeholders
                # This case implies standardized_reg_no was None for all, which should have been caught earlier.
                # However, if an original "no_reg_" from B_extract is the only thing, re-assign a new one.
                if items_for_identifier[0]["standardized_reg_no"] and items_for_identifier[0]["standardized_reg_no"].lower().startswith("no_reg_"):
                    primary_reg_no_for_c = f"no_reg_{no_reg_index_counter}"
                    no_reg_index_counter += 1
                else: # Should ideally not be reached if filtering is correct
                    log_message(f"      Error: Could not determine a primary reg_no for single image {identifier} (std_reg_nos: {current_std_reg_nos_for_identifier}). Using placeholder.", level="ERROR")
                    primary_reg_no_for_c = f"no_reg_{no_reg_index_counter}"
                    no_reg_index_counter += 1
            else:
                log_message(f"      Error: No items for identifier {identifier} to determine primary_reg_no. Skipping.", level="ERROR")
                continue
            
            images_for_C_processing.append({
                "reg_no": primary_reg_no_for_c,
                "img_url": original_img_url,
                "download_path": img_path,
                "source_page_url": source_page_url,
                "source_site": source_site,
                "watermark_description": watermark_desc,
                "candidate_usco_reg_nos": list(r for r in current_std_reg_nos_for_identifier if r and not r.lower().startswith(("no_reg_", "multi")))
            })



    # --- Stage 3: Fix multiple pictures pointing to the same reg_no. Then move selected images to copyright_cn_selected_watermarked and deduplicate ---
    
    # Multiple pictures map to the same reg_no
    reg_no_to_image_map = defaultdict(list)
    for item in images_for_C_processing:
        reg_no_to_image_map[item["reg_no"]].append(item)
    
    for reg_no, image_items in reg_no_to_image_map.items():
        if len(image_items) > 1:
            log_message(f"      Warning: Multiple images for reg_no {reg_no}. Keeping the most colorful one.", level="WARNING")
            prompt = f"I am looking for copyrighted artworks. You help me decide which image to keep. I have {len(image_items)} images. Please analyze the images and return the identifier of the image that I should keep."
            prompt += f"Rules: \n 1. If 2 images are the same, I will keep the one that looks the nicest (no bad borders, high resolution, clear, etc.) \n "
            prompt += f"2. If one artwork is standalone in an images and the same artwork is with some context in another image, I will keep the one with the standalone artwork. \n "
            prompt += f"3. If one image does not have artwork (i.e. only text), I do not keep it. \n "
            prompt += f"4. If 2 images have different artworks, I will keep both. \n\n "
            prompt += f"You return your answer as a list of identifiers of the images I should keep, e.g. ['Image4', 'Image6']. Here is the list of images:\n\n"
            prompt_list = [("text", prompt)]
            for i, item in enumerate(image_items):
                prompt_list += [("text", f"Image{i+1}:\n")]
                prompt_list += [("image_path", item["download_path"])]

            ai_answer = await vertex_genai_multi_async(prompt_list, model_name=Constants.IMAGE_MODEL_FREE_LIMITED, useVertexAI=Constants.IMAGE_MODEL_FREE_LIMITED_VERTEX)
            images_to_keep = get_list(ai_answer)
            
            if images_to_keep:
                for i, item in enumerate(image_items): 
                    if f"Image{i+1}" not in images_to_keep:
                        shutil.copy(item["download_path"], os.path.join(duplicate_dir_C_step, os.path.basename(item["download_path"])))
                        images_for_C_processing = [img for img in images_for_C_processing if img["download_path"] != item["download_path"]]
            
    # Remove non art, collage, screenshot, no features and duplicates
    images_for_C_processing_deduped = deduplicate_selected_images(images_for_C_processing, duplicate_dir_C_step)
        


    ### --- Stage 4: Fetch USCO Info for all registration numbers found on the Chinese Website --- ###
    if master_reg_nos_for_usco_fetch:
        log_message(f"   Fetching USCO details for {len(master_reg_nos_for_usco_fetch)} unique copyright registration numbers (sequentially)...")
        
        driver = None
        for reg_no in master_reg_nos_for_usco_fetch:
            reg_from_df = ip_manager.copyright_dataframe[ip_manager.copyright_dataframe['registration_number'] == reg_no]
            if "title" in reg_from_df.columns and len(reg_from_df) > 0 and reg_from_df['title'].iloc[0]:
                log_message(f"      Skipping USCO fetch for {reg_no} as it already exists in the dataframe.", level="DEBUG")
                continue
        
            if driver is None:
                driver = get_driver()
                
            try:
                log_message(f"      ⛏️ Fetching USCO info for registration number: {reg_no}...")
                usco_result_data = get_info_from_USCO_using_reg_no(reg_no, driver) # Synchronous call
                
                if usco_result_data:
                    log_message(f"      ✅ Successfully fetched USCO info for {reg_no}.", level="DEBUG")
                    ip_manager.add_copyright_to_dataframe(usco_result_data)
                else:
                    log_message(f"      ℹ️ No USCO info found for {reg_no}.", level="DEBUG")
            except Exception as e:
                log_message(f"      ⚠️ Error fetching USCO info for {reg_no}: {e}", level="WARNING")
        if driver:
            driver.quit()
        log_message(f"   Finished fetching USCO details. Processed {len(master_reg_nos_for_usco_fetch)} registration numbers.", level="INFO")



    # --- Stage C: Reverse search and GenAI watermark removal ---
    d_step_input_items = [] 
    for c_input_item in images_for_C_processing_deduped:
        try:
            log_message(f"      Calling C_copyright_process_picture for reg_no '{c_input_item['reg_no']}', image: {c_input_item['download_path']}", level="DEBUG")
            # C_copyright_process_picture returns the same item as input plus "final_image_path" (with "reg_no", "img_url", "source_page_url", "source_site", "watermark_description", "download_path", etc.)
            c_output_item = await C_copyright_process_picture(
                c_input_item=c_input_item,
                copyright_case_base_dir=copyright_case_base_dir,
                watermarked_dir=watermarked_dir,
                reverse_search_output_dir=reverse_search_output_dir,
                reverse_search_final_dir=reverse_search_final_dir,
                duplicate_dir=duplicate_dir_C_step
            )
            if c_output_item is None:
                log_message(f"      C_copyright_process_picture returned None for reg_no '{c_input_item['reg_no']}'", level="WARNING")
                continue
            d_step_input_items.append(c_output_item.copy())
            log_message(f"      C_copyright_process_picture completed for reg_no '{c_input_item['reg_no']}', final image path: {c_output_item['final_image_path']}", level="DEBUG")
            
        except Exception as e_C_process:
            log_message(f"      Error in C_copyright_process_picture for reg_no '{c_input_item['reg_no']}': {e_C_process}, {traceback.format_exc()}", level="ERROR")



    # --- Stage D: Map Processed Copyright Images to USCO Reg Nos using LLM ---
    d_step_output_items = [] 
    if "copyright" in required_ip and d_step_input_items:
        log_message(f"   Mapping {len(d_step_input_items)} processed copyright images to USCO reg_nos using LLM...", level="INFO")
        d_step_output_items = await D_map_reg_nos_to_images_async(
            d_step_input_items=d_step_input_items,
            ip_manager=ip_manager,
            docket=docket
        )
        log_message(f"   LLM mapping complete. {len(d_step_output_items)} image details processed.", level="INFO")

    ### --- Stage 4 : Copy final copyright images from copyright_case_base_dir to the main case images directory and copyright directory ---
    os.makedirs(case_images_directory, exist_ok=True) # Ensure destination exists

    for item_detail in d_step_output_items:
        source_path = item_detail.get("final_image_path")
        # Ensure the destination filename has a .webp extension
        base_dest_filename = os.path.splitext(os.path.basename(source_path))[0]
        dest_filename = f"{base_dest_filename}.webp"
        dest_path = os.path.join(case_images_directory, dest_filename)
        try:
            # Open the source image and save it as WEBP
            with Image.open(source_path) as img:
                # Convert to RGB if it's RGBA or P (palette) to avoid issues with WEBP saving
                if img.mode in ('RGBA', 'P'):
                    img = img.convert('RGB')
                img.save(dest_path, 'WEBP', quality=80) # Adjust quality as needed
                shutil.copy2(dest_path, os.path.join(Constants.local_ip_folder, "Copyrights", dest_filename))
            item_detail["final_image_path"] = dest_path # Update path to new location
            log_message(f"   Copied and converted CN copyright image to '{dest_path}'.", level="DEBUG")

        except Exception as e_copy:
            log_message(f"   Error copying CN copyright image from '{source_path}' to '{dest_path}': {e_copy}", level="ERROR")


    ### --- Stage Final: Assemble Final Return Dictionary --- ###
    return {
        "trademarks": sorted(list(final_trademarks_set)),
        "patents": sorted(list(final_patents_set)),
        "copyright_processed_images": d_step_output_items, # Changed key and value
    }




# Example Usage (can be run with `python -m Scraper.scraper`)
if __name__ == "__main__":
    async def main():
        import pandas as pd
        from DatabaseManagement.ImportExport import get_table_from_GZ
        date = pd.to_datetime("2025-03-14")
        # test_case_docket = "1:25-cv-02710"
        test_case_docket = "1:24-cv-24824"
        # test_case_docket = "1:25-cv-00097"
        test_case_docket = "1:25-cv-05631"
        test_case_docket = "1:25-cv-21509"
        test_case_docket = "1:24-cv-08648"
        case_images_directory = os.path.join(os.getcwd(), "Documents", "IP", "Copyright_Case_Files", "1_25-cv-05631", "images")
        os.makedirs(case_images_directory, exist_ok=True)

        plaintiff_name = None # Initialize plaintiff_name

        case_where_clause = f"docket = '{test_case_docket}'"
        # Using force_refresh=False to align with previous behavior for these lookups.
        df_case = get_table_from_GZ("tb_case", force_refresh=False, where_clause=case_where_clause)

        if not df_case.empty:
            plaintiff_id = df_case["plaintiff_id"].values[0]
            
            plaintiff_where_clause = f"id = {plaintiff_id}" # id is numeric as confirmed
            df_plaintiff = get_table_from_GZ("tb_plaintiff", force_refresh=False, where_clause=plaintiff_where_clause)
            
            if not df_plaintiff.empty:
                plaintiff_name = df_plaintiff["plaintiff_name"].values[0]
            else:
                print(f"Plaintiff with ID {plaintiff_id} not found for case {test_case_docket}.")
                # plaintiff_name remains None
        else:
            print(f"Case with docket {test_case_docket} not found.")
            # plaintiff_name remains None

        if plaintiff_name: # Proceed only if plaintiff_name was found
            print(f"Scraping data for case: {test_case_docket} (Plaintiff: {plaintiff_name})")

            # Initialize IP manager and load copyright dataframe
            ip_manager = IPTrackingManager()
            ip_manager.load_copyright_dataframe_for_case(plaintiff_id)

            data = await scrape_case_data(date, test_case_docket, plaintiff_name, ["patent", "trademark", "copyright"], case_images_directory, ip_manager)

            # Export copyright dataframe to database
            ip_manager.export_copyright_dataframe_to_database()

            print("\n--- Final Result ---")
            print(json.dumps(data, indent=2))
        else:
            print(f"Could not retrieve plaintiff name for case {test_case_docket}. Skipping scrape.")

    asyncio.run(main())