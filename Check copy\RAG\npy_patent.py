import os
import numpy as np
import re
from sklearn.metrics.pairwise import cosine_similarity
from collections import Counter
from langfuse import observe
import langfuse
from Check.RAG.RAG_Inference import get_siglip_embeddings


# Function to find the most similar image
@observe()
def find_most_similar_patent_image(cases_df, plaintiff_df, query_image_paths, top_n=1, similarity_threshold_images=0.6, similarity_threshold_text=0.25):

    if len(query_image_paths) == 0:
        return []
    
    # Get SigLIP embeddings for the query images
    query_embeddings = get_siglip_embeddings(query_image_paths, "image")


    # Extract SigLIP embeddings from the structured arrays
    from Check.RAG.RAG_Inference import structured_patent_image_array, structured_patent_text_array
    embeddings_image = np.array([entry['embedding_siglip'] for entry in structured_patent_image_array])
    embeddings_text = np.array([entry['embedding_siglip'] for entry in structured_patent_text_array])

    # Compute cosine similarity between all query embeddings and all stored embeddings
    similarities_image = cosine_similarity(query_embeddings, embeddings_image)
    similarities_text = cosine_similarity(query_embeddings, embeddings_text)

    all_top_matches = []
    all_matches = []

    for i, similarity_scores in enumerate(similarities_image):
        # Sort results by similarity score in descending order
        sorted_indices = np.argsort(similarity_scores)[::-1]
        top_matches = []

        top1 = sorted_indices[0]
        top2 = sorted_indices[1]
        sim1 = similarity_scores[top1]
        similarity_over_top2 = sim1-similarity_scores[top2]
        similarity_over_top2_percentage = similarity_over_top2/sim1

        for idx in sorted_indices[:top_n]:
            if similarity_scores[idx] >= similarity_threshold_images:
                match_info = {
                    'query_image_path': query_image_paths[i],
                    'text': structured_patent_image_array[idx]['text'],
                    'patent_number': structured_patent_image_array[idx]['patent_number'],
                    'filename': structured_patent_image_array[idx]['filename'],
                    'full_filename': structured_patent_image_array[idx]['full_filename'],
                    'similarity': similarity_scores[idx],
                    'plaintiff_name': structured_patent_image_array[idx]['plaintiff_name'],
                    'docket': structured_patent_image_array[idx]['docket'],
                    'number_of_cases': structured_patent_image_array[idx]['number_of_cases']
                }
                top_matches.append(match_info)

        all_top_matches.extend(top_matches)
        all_matches.append({'plaintiff_name': structured_patent_image_array[top1]['plaintiff_name'], 'match_type': 'image', 'text': structured_patent_image_array[top1]['text'], 'similarity': f"{sim1:.2f}", 'similarity_over_top2': f"{similarity_over_top2:.2f}", 'similarity_over_top2_%': f"{similarity_over_top2_percentage:.2f}"})
    

    for i, similarity_scores in enumerate(similarities_text):
        sorted_indices = np.argsort(similarity_scores)[::-1]
        top_matches = []

        top1 = sorted_indices[0]
        top2 = sorted_indices[1]
        sim1 = similarity_scores[top1]
        similarity_over_top2 = sim1-similarity_scores[top2]
        similarity_over_top2_percentage = similarity_over_top2/sim1

        for idx in sorted_indices[:top_n]:
            if similarity_scores[idx] >= similarity_threshold_text:
                match_info = {
                    'query_image_path': query_image_paths[i],
                    'text': structured_patent_text_array[idx]['text'],
                    'patent_number': structured_patent_text_array[idx]['patent_number'],
                    'filename': structured_patent_text_array[idx]['filename'],
                    'full_filename': structured_patent_text_array[idx]['full_filename'],
                    'similarity': similarity_scores[idx],
                    'plaintiff_name': structured_patent_text_array[idx]['plaintiff_name'],
                    'docket': structured_patent_text_array[idx]['docket'],
                    'number_of_cases': structured_patent_text_array[idx]['number_of_cases']
                }
                top_matches.append(match_info)  

        all_top_matches.extend(top_matches)
        all_matches.append({'plaintiff_name': structured_patent_text_array[top1]['plaintiff_name'], 'match_type': 'text', 'text': structured_patent_text_array[top1]['text'], 'similarity': f"{sim1:.2f}", 'similarity_over_top2': f"{similarity_over_top2:.2f}", 'similarity_over_top2_%': f"{similarity_over_top2_percentage:.2f}"})
    
    
    # patent_numbers = [match['patent_number'] for match in all_top_matches]
    texts = [match['text'] for match in all_top_matches]

    final_matches = []
    if len(set(texts)) == 1:
        single_match = all_top_matches[0]
        single_match['full_patent'] = get_patent_pages(cases_df, plaintiff_df, single_match)
        final_matches = [single_match]
    elif len(set(texts)) > 1:
        # count how many times each text appears
        text_counts = Counter(texts)

        # find the one that appear the most and the second most 
        most_common_text = text_counts.most_common(1)[0][0]
        most_common_text_count = text_counts.most_common(1)[0][1]
        second_most_common_text = text_counts.most_common(2)[1][0]
        second_most_common_text_count = text_counts.most_common(2)[1][1]

        if most_common_text_count > 1.5 * second_most_common_text_count:
            most_common_match = [match for match in all_top_matches if match['text'] == most_common_text][0]
            most_common_match['full_patent'] = get_patent_pages(cases_df, plaintiff_df, most_common_match)
            final_matches = [most_common_match]
        else:
            most_common_match = [match for match in all_top_matches if match['text'] == most_common_text][0]
            most_common_match['full_patent'] = get_patent_pages(cases_df, plaintiff_df, most_common_match)
            second_common_match = [match for match in all_top_matches if match['text'] == second_most_common_text][0]
            second_common_match['full_patent'] = get_patent_pages(cases_df, plaintiff_df, second_common_match)
            final_matches = [most_common_match, second_common_match]

    all_matches.sort(key=lambda x: x['similarity'] + x['similarity_over_top2'] , reverse=True) # sorting by sim1 + (sim1/sim2-1)
    langfuse.get_client().update_current_span(metadata=all_matches[:10])   
    
    return final_matches

def get_patent_pages(cases_df, plaintiff_df, similarity_match):
    # Helper function to extract page number robustly using regex
    def extract_page_number(filename_str):
        match = re.search(r'page(\d+)', filename_str)
        if match:
            return int(match.group(1))
        elif "_full" in filename_str: # the certificate comes first
            return 0
        
        # Fallback if page number is not found. This might indicate an unexpected filename format.
        # Depending on requirements, you could raise an error or return a default (e.g., 0 or float('inf')).
        print(f"Warning: Could not extract page number from '{filename_str}'. Defaulting to 0 for sorting.")
        return 99

    if isinstance(similarity_match['filename'],str):
        plaintiff_id = plaintiff_df.loc[plaintiff_df['plaintiff_name'] == similarity_match['plaintiff_name'], 'id'].iloc[0]
        row = cases_df[(cases_df['docket'] == similarity_match['docket']) & (cases_df['plaintiff_id'] == plaintiff_id)].iloc[0]
        all_pages = [key for key in row['images']['patents'].keys() if row['images']['patents'][key]['full_filename'][0] == similarity_match['full_filename']]
        full_patent = all_pages + [similarity_match['full_filename']]
        # Sort by page number
        full_patent = sorted(full_patent, key=extract_page_number)
    else:
        full_patent = similarity_match['filename'] + [similarity_match['full_filename']]
        # Sort by page number
        full_patent = sorted(full_patent, key=extract_page_number)
    return full_patent