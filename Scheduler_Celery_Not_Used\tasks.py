import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from Scheduler_Celery_Not_Used.celery_app import celery
from datetime import datetime
from email_sender import send_email_report
from Alerts.zz_Get_Alerts import get_alerts
import sqlite3
from logdata import db_path, create_new_run
import pytz
import multiprocessing
from celery.signals import worker_init
from celery.utils.log import get_task_logger

@celery.task(bind=True)
def task_get_daily_cases(self):
    print(f"Task ID: {self.request.id}")
    print(f"now the time is {datetime.now()}, but in NY timezone it is {datetime.now(pytz.timezone('America/New_York'))}")
    print("get_daily_cases")

    current_process = multiprocessing.current_process()
    logger = get_task_logger(__name__)
    logger.info(f"Process name: {current_process.name}")
    logger.info(f"Process daemon status: {current_process.daemon}")
    logger.info(f"Current process details: {current_process}")
    selected_date = datetime.now(pytz.timezone('America/New_York')).date()
    loop_back_days = 1
    run_id = create_new_run()
    get_alerts(run_id, selected_date, loop_back_days, force_redo=False)
    send_email_report(run_id)
    
@celery.task(bind=True)
def task_update_subscribed_cases(self):
    print(f"Task ID: {self.request.id}")
    print(f"now the time is {datetime.now()}")
    print("update_subscribed_cases")
    # send_email_report()

@celery.task(bind=True)
def task_test(self):
    print(f"Task ID: {self.request.id}")
    print(f"now the time is {datetime.now()}")
    print("Email report sent!")

    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    
    # Get the most recent completed run
    c.execute('''
        SELECT id 
        FROM runs 
        WHERE status = 'Completed'
        ORDER BY RANDOM()
        LIMIT 1
    ''')
    
    result = c.fetchone()
    conn.close()
    
    if result:
        run_id = result[0]
        print(f"Sending email report for run ID: {run_id}")
        send_email_report(run_id)
        print("Email sent successfully!")
    else:
        print("No completed runs found in the database")

@worker_init.connect
def print_worker_info(**kwargs):
    logger = get_task_logger(__name__)
    logger.info(f"Worker pool type: {kwargs['sender'].pool.__class__.__name__}")
    logger.info(f"Worker configuration: {kwargs['sender'].app.conf}")
