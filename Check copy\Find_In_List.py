from DatabaseManagement.ImportExport import get_table_from_GZ, insert_and_update_df_to_GZ_batch
import json
import zlib
import base64

import time
from Check.Data_Cache import get_cached_brand_list
from fuzzywuzzy import fuzz, process
import re
from langfuse import observe
from Alerts.Plaintiff import remove_acronyms

EXCLUDE_WORDS = frozenset(['the', 'and', 'of', 'for', 'a', 'an', 'on', 'inc', 'corp', 'llc', 'ltd', 'corporation', 'labs', 'lab', 'holding', 'sa', 'art', 'ltd', 'de', 'united states', 'inc.', 'incorporated', 'corp.', 'corporation', 'llc', 'limited liability company', 'llp', 'limited liability partnership', 'lp', 'limited partnership', 'pc', 'professional corporation', 'pllc', 'professional limited liability company', 'canada', 'inc.', 'incorporated', 'ltd.', 'limited', 'corp.', 'corporation', 'co.', 'company', 'ulc', 'unlimited liability company', 'united kingdom', 'ltd.', 'private limited company', 'plc', 'public limited company', 'llp', 'limited liability partnership', 'unlimited', 'unlimited company', 'ireland', 'ltd.', 'private company limited by shares', 'plc', 'public limited company', 'dac', 'designated activity company', 'uc', 'unlimited company', 'germany', 'gmbh', 'gesellschaft mit beschränkter haftung', 'ug (haftungsbeschränkt)', 'unternehmergesellschaft (haftungsbeschränkt)', 'ag', 'aktiengesellschaft', 'kg', 'kommanditgesellschaft', 'gmbh & co. kg', 'gesellschaft mit beschränkter haftung & compagnie kommanditgesellschaft', 'france', 'sarl', 'société à responsabilité limitée', 'sas', 'société par actions simplifiée', 'sa', 'société anonyme', 'snc', 'société en nom collectif', 'spain', 'sl', 'sociedad limitada', 'sa', 'sociedad anónima', 'slne', 'sociedad limitada nueva empresa', 'italy', 's.r.l.', 'società a responsabilità limitata', 's.p.a.', 'società per azioni', 's.a.p.a.', 'società in accomandita per azioni', 'the netherlands', 'b.v.', 'besloten vennootschap', 'n.v.', 'naamloze vennootschap', 'nordic countries', 'sweden', 'ab', 'aktiebolag', 'denmark', 'a/s', 'aktieselskab', 'aps', 'anpartsselskab', 'norway', 'as', 'aksjeselskap', 'finland', 'oy', 'osakeyhtiö', 'switzerland', 'ag', 'aktiengesellschaft', 'gmbh', 'gesellschaft mit beschränkter haftung', 'sa', 'société anonyme', 'united kingdom'])


COMMON_ENGLISH_WORDS = [
    "the", "be", "to", "of", "and", "a", "in", "that", "have", "I",
    "it", "for", "not", "on", "with", "he", "as", "you", "do", "at",
    "this", "but", "his", "by", "from", "they", "we", "say", "her", "she",
    "or", "an", "will", "my", "one", "all", "would", "there", "their", "what",
    "so", "up", "out", "if", "about", "who", "get", "which", "go", "me",
    "when", "make", "can", "like", "time", "no", "just", "him", "know", "take",
    "people", "into", "year", "your", "good", "some", "could", "them", "see", "other",
    "than", "then", "now", "look", "only", "come", "its", "over", "think", "also",
    "back", "after", "use", "two", "how", "our", "work", "first", "well", "way",
    "even", "new", "want", "because", "any", "these", "give", "day", "most", "us"
]

def find_brand_in_trademark_text(brand, plaintiff_df, partial_match=True):

    brand_list = get_cached_brand_list()

    # Brand == Trademark_Text
    match_items = [item for item in brand_list if item["trademark_text"] == brand.lower()]
    # if not plaintiff: # Element is in wd elements! => not good => must be entire word
    #     # Brand in Trademark_Text
    #     plaintiff = [item for item in brand_list if brand.lower() in item["trademark_text"]]
    #     confidence = -0.3

    if not match_items and partial_match == True:
        # Brand word in Trademark_Text as whole word. Created problems: "double din" key word got associated with "double wide" trademark
        words = [word for word in brand.lower().split() if word not in EXCLUDE_WORDS]
        match_items = [item for item in brand_list if any(word == item_word for word in words for item_word in item["trademark_text"].split() if len(word) > 3)]
        confidence = -0.5

    for item in match_items:
        item["plaintiff_name"] = plaintiff_df.loc[plaintiff_df['id'] == item['plaintiff_id'], 'plaintiff_name'].iloc[0]
        item["trademark_url"] = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{item['plaintiff_id']}/high/{item['filename']}"
        item["trademark_certificate_url"] = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{item['plaintiff_id']}/high/{item['full_filename'][0]}"

    # if not plaintiff:  # This led to bad result: image was "Genshin Impact" and one of the word of Sega was "shin", then "shin" is in "Genshin Impact"
        # Trademark_Text word in Brand. We might not need this given the "Brand word in Trademark_Text"
        # plaintiff = [item[0] for item in brand_list if (item[1] in brand.lower() and len(item[1]) > 3)]
    
    # Remove duplicates
    unique_items = []
    seen_keys = set()
    for item in match_items:
        reg_no = tuple(item["reg_no"]) if isinstance(item.get("reg_no"), list) else item.get("reg_no")
        key = (item.get("plaintiff_id"), reg_no)
        if key not in seen_keys:
            seen_keys.add(key)
            unique_items.append(item)

    return unique_items




def find_name_in_plaintiff_list(brand, company_name, plaintiff_df):
    # Why not just "name" in plaintiff: hp in hupshpuppies! 
    # Why not jjust any word of "name" == any word of plaintiff: Ghenshing Impact would match to Creative Impact! => all word of "name" need to be there 
    # Should we use LLM to confirm?

    if company_name.lower() == "unknown": # the LLM says unknow if it does not know. But a plaintiff can be called "unknown"
        return []
    
    company_name = remove_acronyms(company_name)

    match_items = []
    
    filtered_df = plaintiff_df[(plaintiff_df['id'] != 9) & (plaintiff_df['plaintiff_name'].str.lower() == company_name.lower())]
    if not filtered_df.empty:
        matching_plaintiff_ids = filtered_df['id'].tolist()
    else:
        # If no exact match, split the name into words (excluding any words in EXCLUDE_WORDS)
        words = [word for word in company_name.lower().split() if word not in EXCLUDE_WORDS]
        # Filter rows where every word in words is present in the plaintiff name (after splitting the name)
        filtered_df = plaintiff_df[(plaintiff_df['id'] != 9) &
            (plaintiff_df['plaintiff_name'].str.lower().apply(
                lambda _name: all(word in _name.split() for word in words)
            ))
        ]
        matching_plaintiff_ids = filtered_df['id'].tolist()


    match_items = []
    for matching_plaintiff_id in matching_plaintiff_ids:
        brand_list = get_cached_brand_list()
        brand_list = [item for item in brand_list if item["plaintiff_id"] == matching_plaintiff_id]

        if brand_list:
            match_brands = [item for item in brand_list if item["trademark_text"] == brand.lower()]
            if not match_brands:
                # Brand in Trademark_Text
                match_brands = [item for item in brand_list if brand.lower() in item["trademark_text"]]
            if not match_brands:
                # Brand word in Trademark_Text as whole word.
                words = [word for word in brand.lower().split() if word not in EXCLUDE_WORDS]
                match_brands = [item for item in brand_list if any(word == item_word for word in words for item_word in item["trademark_text"].split() if len(word) > 3)]

            if not match_brands:
                # use fuzzy matching between name and all the item["trademark_text"] in brand_list, and take the one with the highest score
                scores = [fuzz.partial_ratio(brand.lower(), item["trademark_text"]) for item in brand_list]
                best_score = max(scores)
                best_index = scores.index(best_score)
                best_match = brand_list[best_index]
                match_brands = [best_match]

            match_brands[0]["trademark_url"] = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{matching_plaintiff_id}/high/{match_brands[0]['filename']}"
            match_brands[0]["trademark_certificate_url"] = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{matching_plaintiff_id}/high/{match_brands[0]['full_filename'][0]}"
            match_brands[0]["plaintiff_name"] = plaintiff_df.loc[plaintiff_df['id'] == matching_plaintiff_id, 'plaintiff_name'].iloc[0]
            match_items.append(match_brands[0])
        else:
            match_items.append({
                "plaintiff_id": matching_plaintiff_id,
                "plaintiff_name": plaintiff_df.loc[plaintiff_df['id'] == matching_plaintiff_id, 'plaintiff_name'].iloc[0],
                # "trademark_text": "",
                # "trademark_image": "",
                # "full_filename": "",
                # "reg_no": "",
                # "int_cls_list": "",
                # "trademark_url": "",
                # "trademark_certificate_url": ""
            })
    
    return match_items


# def find_plaintiff_in_text(plaintiff_df, text):
#     plaintiff_names = plaintiff_df['plaintiff_name'].tolist()
#     all_matches = set()
#     for plaintiff_name in plaintiff_names:
#         if plaintiff_name.lower() in text.lower():
#             all_matches.add(plaintiff_name)
#     return list(all_matches)

@observe()
def find_brand_in_text(text, plaintiff_df):
    brand_list = get_cached_brand_list()
    text_lower = text.lower()
    # match_items = [item for item in brand_list if re.search(r"\b" + re.escape(item["trademark_text"]) + r"\b", text_lower)]

    match_items = []
    seen_trademark_text = set()
    for item in brand_list:
        trademark_text = item["trademark_text"]
        if trademark_text.lower() in COMMON_ENGLISH_WORDS:
            continue

        trademark_lower = trademark_text.lower()
        if trademark_lower in seen_trademark_text:
            continue
        seen_trademark_text.add(trademark_lower)
        
        # 1. Exact, whole-word match (fastest)
        pattern = r"\b" + re.escape(trademark_lower) + r"\b"
        if re.search(pattern, text_lower):
            item["score"] = 100
            match_items.append(item)
            continue  # Skip to the next item if we find an exact match

        # 2. Fuzzy matching (slower, only if no exact match)
        # Use token_sort_ratio instead of partial_ratio
        score = fuzz.token_sort_ratio(trademark_lower, text_lower)
        if score >= 95:  # Adjust threshold as needed.  Token sort ratio is stricter.
            item["score"] = score
            match_items.append(item)

    for item in match_items:
        item["plaintiff_name"] = plaintiff_df.loc[plaintiff_df['id'] == item['plaintiff_id'], 'plaintiff_name'].iloc[0]
        item["trademark_url"] = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{item['plaintiff_id']}/high/{item['filename']}"
        item["trademark_certificate_url"] = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{item['plaintiff_id']}/high/{item['full_filename'][0]}"
            
    return list(match_items)




if __name__ == "__main__":
    plaintiff_df = get_table_from_GZ("tb_plaintiff")
    cases_df = get_table_from_GZ("tb_case")
    print(find_name_in_plaintiff_list("Dior", plaintiff_df))
    print(find_brand_in_trademark_text("Dior", plaintiff_df)[0])
