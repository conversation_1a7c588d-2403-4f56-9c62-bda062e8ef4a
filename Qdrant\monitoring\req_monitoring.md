8. Monitoring and Observability Requirements
A comprehensive monitoring solution shall be implemented using a self-hosted Grafana instance, integrating metrics from Qdrant, Supabase (PostgreSQL), and the host server(s).

•	8.1. Grafana Instance:
o	A Grafana instance must be deployed (self-hosted).
o	A Prometheus instance must be deployed and configured as the primary data source for Grafana.

•	8.2. Qdrant Monitoring:
o	Qdrant's configuration must enable the Prometheus metrics endpoint (typically enabled by default at /metrics).
o	Prometheus must be configured to scrape metrics from the Qdrant /metrics endpoint(s).
o	The official Qdrant dashboard template (ID: 23033 or latest version) must be imported into Grafana.
o	Key Monitored Metrics (Adapt from template):
	API Request Rate (Total, gRPC, REST) & Error Rate (HTTP status codes, gRPC codes)
	Query Latency (P99, P95, P50 for Search/Recommend/Scroll/etc.)
	Collection Status (Green/Yellow/Red/Grey) & Segment Counts (Total, Indexed, Optimized)
	Points Count & Indexed Vectors Count (Approximate) per collection
	Optimizer Status/Progress (Pending operations, errors)
	RAM Usage (RSS, Mapped Memory if applicable)
	CPU Usage
	WAL Size/Operations

•	8.3. PostgreSQL Monitoring:
o	A PostgreSQL exporter (e.g., postgres_exporter) compatible with the Supabase underlying PostgreSQL version must be deployed and configured to connect to the Supabase database.
o	Prometheus must be configured to scrape metrics from the PostgreSQL exporter.
o	A comprehensive PostgreSQL dashboard template (e.g., from Grafana Labs official or a well-regarded community template like ID 9628) must be imported into Grafana and potentially adapted.
o	Key Monitored Metrics (Adapt from template):
	Active/Idle Connections & Max Connections Utilization
	Transactions per second (TPS) & Query Duration (Avg, P95, P99)
	Identification of Slow Queries
	Index Hit Rate & Sequential Scan Rate
	Cache Hit Rate (Buffer Cache)
	Disk Usage (Total size, Free space for data partitions)
	Replication Lag (if applicable)
	Database Locks & Deadlocks

•	8.4. Host Server Monitoring:
o	A node exporter (e.g., node_exporter) must be installed and running on the server(s) hosting Qdrant, Supabase, and Grafana/Prometheus.
o	Prometheus must be configured to scrape metrics from the node exporter(s).
o	A standard Node Exporter dashboard template (e.g., "Node Exporter Full" ID 1860 or similar) must be imported into Grafana.
o	Key Monitored Metrics (Adapt from template):
	CPU Utilization (Per core and total, System/User/Idle/IOwait)
	RAM Usage (Total, Used, Free, Cached, Swap)
	Disk I/O (Read/Write Ops/s, Read/Write Bytes/s, Latency/Await time)
	Disk Space Usage (Percentage used and absolute free space, especially for partitions holding Qdrant data, WAL, snapshots, and PostgreSQL data)
	Network Traffic (In/Out Bytes/s, Packets/s, Errors)
	System Load Average

•	8.5. Alerting:
o	Grafana Alerting or Prometheus Alertmanager must be configured.
o	Alert rules must be defined for critical conditions, including but not limited to:
	Qdrant: High API error rates (>1%), sustained high query latency (> X ms P99), collection status Red/Grey, low indexed vector percentage, low disk space for Qdrant storage, high CPU/RAM saturation.
	Supabase/PostgreSQL: High query latency, high connection count nearing limit, low index hit rate, low cache hit rate, low disk space for DB data, replication lag (if applicable), high number of locks/deadlocks.
	Host: High CPU utilization (>90% sustained), low available RAM (<10%), low disk space (<15%), high disk I/O latency, network errors.
o	Alert notifications must be configured to reach the appropriate operations team (e.g., via Email).
