import os, sys, cv2, os, fitz, time, shutil
from typing import Optional
sys.path.append(os.getcwd())
from logdata import log_message
from Alerts.PicturesProcessing.ProcessPicturesShared import find_complaint_pdf_path
from IP.Patent import get_patent_data_by_reg_no, get_patent_data_by_name, clean_reg_no, get_design_page_numbers
from multiprocessing import Pool
from Alerts.PicturesProcessing.OCRProcessor import OCRProcessor
from AI.GC_VertexAI import vertex_genai_multi
from AI.LLM_shared import get_json, get_json_list, get_list
from AI.GC_VertexAI import vertex_genai_multi_async
from Alerts.IPTrackingManager import IPTrackingManager # Added import
from langfuse import observe
import langfuse
import asyncio # Added for asyncio.gather
import Common.Constants as Constants

pattent_pattern = r'US\sD?[\d]+(?:\s*,\s*[\d]+)*(?:\s*S)?(?:\s*B\d)?'

@observe(capture_input=False, capture_output=False)
async def process_patent_exhibit(df, index, case_directory, pdf_file_path) -> tuple[bool, list[str]]:
    """
    Processes a potential patent exhibit PDF to find patent registration numbers using an LLM.
    If numbers are found, attempts to fetch data from USPTO and process the patent PDFs.

    Args:
        df: DataFrame containing case information.
        index: Index of the current case in the DataFrame.
        case_directory: Path to the case directory.
        pdf_file_path: Path to the PDF file suspected to be a patent exhibit.

    Returns:
        A tuple: (bool indicating if a patent exhibit was successfully processed, list of unique cleaned patent registration numbers found).
    """
    prompt = """I am looking for the patent registration number(s) in this legal filing. Please help me find it (them).
    The patent registration number is on the 1st page of the patent, and in the format of US D? xxx,xxx S? Bx? usually with 6 to 8 numbers
    You return your answer as a list of registration numbers: ["reg_no1", "reg_no2", ...], where is reg_no is a unique Patent Registration Number. Make sure to put each reg_no in double quotes.
    Only include the unique patent registration number of each patent (i.e. do not include any references to other patents). It is very well possible that there is only one patent.
    """
    print(f"\033[33m 📥 Processing patent exhibit: {pdf_file_path}\033[0m")
    prompt_list = [("text", prompt), ("pdf_path", pdf_file_path)]
    ai_answer = await vertex_genai_multi_async(prompt_list, model_name=Constants.SMART_MODEL_FREE)
    patents_identified = get_list(ai_answer)
    cleaned_patents_identified = []

    if not patents_identified:
        print(f"\033[91m!!!!! NO PATENT REGISTRATION NUMBER FOUND in exhibit: ai_answer = {ai_answer}\033[0m")
        return False, []
    else:
        plaintiff_id = df.at[index, "plaintiff_id"]
        # Clean registration numbers before passing them to get_patent_data_by_reg_no
        cleaned_patents_identified = [clean_reg_no(reg_no) for reg_no in patents_identified]
        # Ensure uniqueness
        cleaned_patents_identified = list(set(cleaned_patents_identified))
        
        updated_patent_list = await get_patent_data_by_reg_no(cleaned_patents_identified, plaintiff_id)
        success = await process_patents_pdfs_from_uspto(df, index, case_directory, updated_patent_list)

        # Update images_status if successful
        if success:
            df.at[index, 'images_status']['patent_status']['exhibit']['count'] += len(updated_patent_list)
            df.at[index, 'images_status']['patent_status']['exhibit']['steps'].append(os.path.dirname(pdf_file_path).split("\\")[-1])
            return True, cleaned_patents_identified # Return True and the found reg numbers
        else:
            # Even if processing fails, we found the numbers in the exhibit
            langfuse.get_client().update_current_span(
                input={
                    "CaseIndex": index,
                    "PDFFilePath": os.path.basename(pdf_file_path)
                },
                output={
                    "PatentsIdentifiedByLLM": patents_identified if 'patents_identified' in locals() else 'N/A',
                    "CleanedPatentsCount": len(cleaned_patents_identified) if 'cleaned_patents_identified' in locals() else 0,
                    "USPTO_FetchSuccess": True if 'updated_patent_list' in locals() and updated_patent_list else False,
                    "ProcessPDFsSuccess": success if 'success' in locals() else False
                }
            )
            return False, cleaned_patents_identified # Return False but still return found reg numbers

@observe(capture_input=False, capture_output=False)
async def get_and_process_patents_from_uspto_using_regnos(df, index, case_directory, patents_identified_list, ip_manager: IPTrackingManager):
    """
    Fetches patent data from USPTO using registration numbers, processes the PDFs,
    updates the dataframe, and records findings using IPTrackingManager.

    Args:
        df: DataFrame containing case data.
        index: Index of the current case in the DataFrame.
        case_directory: Path to the case directory.
        patents_identified_list: List of patent registration numbers found.
        ip_manager: Instance of IPTrackingManager to track IP processing state.

    Returns:
        bool: True if patents were successfully processed, False otherwise.
    """
    print(f"\033[33m 📥 Get and process patents using reg_no from AI: {patents_identified_list}\033[0m")
    start_time = time.time()
    plaintiff_id = df.at[index, "plaintiff_id"]
    
    cleaned_patents_identified_list = [clean_reg_no(reg_no) for reg_no in patents_identified_list] # Clean reg_nos before passing to get_patent_data_by_reg_no
    updated_patent_list = await get_patent_data_by_reg_no(cleaned_patents_identified_list, plaintiff_id)
    print(f"\033[32m 🔥 USPTO Patent API time: {len(updated_patent_list)} in {format(time.time() - start_time, '.1f')} seconds\033[0m")
    
    # Process patents using the common helper function
    success = await process_patents_pdfs_from_uspto(df, index, case_directory, updated_patent_list)

    # Update images_status and ip_manager state if successful
    if success:
        df.at[index, 'images_status']['patent_status']['byregno']['count'] += len(updated_patent_list)
        # Update ip_manager state for each found patent
        cleaned_reg_nos = [clean_reg_no(patent['document_id']) for patent in updated_patent_list]
        location_id = f"USPTO"
        ip_manager.record_finding('patent', location_id, cleaned_reg_nos)

    langfuse.get_client().update_current_span(
        input={
            "CaseIndex": index,
            "NumPatentsToIdentify": len(patents_identified_list),
            "Source": "LLM/MainDoc/ChineseWebsite/Unresolved" # Assuming this is a fixed string based on the f-string
        },
        output={
            "CleanedPatentsToFetch": len(cleaned_patents_identified_list) if 'cleaned_patents_identified_list' in locals() else 0,
            "PatentsFromUSPTO_API": len(updated_patent_list) if 'updated_patent_list' in locals() else 0,
            "ProcessPDFsSuccess": success if 'success' in locals() else False,
            "PatentGoalMetAfter": ip_manager.is_goal_met('patent')
        }
    )
    return success



@observe(capture_input=False, capture_output=False)
async def get_and_process_patents_from_uspto_using_plaintiff_name(df, index, case_directory, plaintiff_df, ip_manager: IPTrackingManager):
    """
    Searches for patents by plaintiff name, processes relevant ones, updates the dataframe,
    and records findings using IPTrackingManager.

    Args:
        df: DataFrame containing case data.
        index: Index of the current case in the DataFrame.
        case_directory: Path to the case directory.
        plaintiff_df: DataFrame containing plaintiff information.
        ip_manager: Instance of IPTrackingManager to track IP processing state.

    Returns:
        bool: True if patents were successfully found and processed, False otherwise.
    """
    plaintiff_id = df.at[index, "plaintiff_id"]
    plaintiff_name = plaintiff_df["plaintiff_name"].loc[plaintiff_df["id"] == plaintiff_id].values[0]
    print(f"\033[33m 📥 Get and process patents using plaintiff name: {plaintiff_name}\033[0m")

    # Find complaint pdf path
    complaint_pdf_path = find_complaint_pdf_path(case_directory)

    if not complaint_pdf_path:
        print(f"\033[91m!!!!! Patent by Name: No complaint pdf found. Ending search by name.\033[0m")
        return False
    
    # Get patent data asynchronously
    start_time = time.time()

    prompt_text = f"""I want to find the patent(s) on USPTO related with this legal case. The name of the plaintiff on the complaint is "{plaintiff_name}".
            Your job is to design the search criteria based on the complaint filed with the court. The fields available are: inventor, assignee, and applicant
            Usually using the plaintiff name in the assignee or applicant field will give good results.
            But if the plaintiff name if too short or too generic, this might not be good enough. 
            If the name is specific enough you should not include company codes (such as LLC, Inc, SA, etc), but when the plaintiff name is too short or too generic (like 2 letters or a very common name), it might be a good idea to unclude it.
            Please design the search criteria in the format of {{"inventor": "inventor_name", "assignee": "assignee_name", "applicant": "applicant_name"}}. 
            If there is not enough information to design a good search criteria, just just leave the field(s) empty, e.g. {{"assignee": "", "applicant": "", "inventor": ""}}"""
    prompt_list = [("text", prompt_text), ("pdf_path", complaint_pdf_path)]
    ai_answer = await vertex_genai_multi_async(prompt_list, model_name=Constants.SMART_MODEL_FREE)
    names = get_json(ai_answer)

    patent_list, search_term_used = await get_patent_data_by_name(names, plaintiff_name, nb_results=50, plaintiff_id=plaintiff_id)
    print(f"\033[32m 🔥 USPTO Patent API time: {len(patent_list)} in {format(time.time() - start_time, '.1f')} seconds\033[0m")

    if not patent_list or len(patent_list) == 0:
        print(f"\033[91m!!!!! NO PATENT FOUND: stopping the search by name\033[0m")
        return False


    unique_assignees = set(patent['assignee'] for patent in patent_list)
    if len(patent_list) > 5 or len(unique_assignees) > 1:
        relevant_patent_list = []
        prompt = "Based on the complaint filed with the court, you need to identify the most relevant patents that are related to the plaintiff's claim. The plaintiff is: " + plaintiff_name + ". This is the complaint filed with the court: \n\n" 
        prompt_list = [("text", prompt), ("pdf_path", complaint_pdf_path)]
        
        prompt = "\n\nHere is an overview of the patents: \n\n"
        for i, patent in enumerate(patent_list):
            prompt += f"Patent {i+1}: DocumentID: {patent['document_id']}, Title: {patent['text']}, Assignee: {patent['assignee']}, Applicant: {patent['applicant']}, Inventors: {patent['inventors']}, Abstract: {patent['abstract']}\n\n"

        prompt += f'Based on the above information, taking in consideration the name of the plaintiff ({plaintiff_name}), the title of the patents, and the abstract, you need to identify the most relevant patents (max 20) that are related to the plaintiff claim. You provide the DocumentID of the patents that you think are most relevant in a list: ["US-XXXXX", "US-XXXXX", "US-XXXXX"]. \n\n'
        prompt_list.append(("text", prompt))
        ai_answer = await vertex_genai_multi_async(prompt_list, model_name=Constants.SMART_MODEL_FREE)
        relevant_patent_document_id_list = get_list(ai_answer)
        relevant_patent_list = [patent for patent in patent_list if patent['document_id'] in relevant_patent_document_id_list]
        print(f"💡 Relevant patents determined by LLM: {[patent['document_id'] for patent in relevant_patent_list]}[0m")
    else: 
        relevant_patent_list = patent_list
    
    # Process patents using the common helper function
    success = await process_patents_pdfs_from_uspto(df, index, case_directory, relevant_patent_list)
    
    # If successful, mark for validation and update state
    if success:
        df.at[index, 'validation_status'] = 'review_required'
        # Update images_status field
        df.at[index, 'images_status']['patent_status']['byname']['count'] += len(relevant_patent_list)
        df.at[index, 'images_status']['patent_status']['byname']['ai_search_term'] = names
        df.at[index, 'images_status']['patent_status']['byname']['search_term_used'] = search_term_used
        # Update ip_manager state for each relevant patent found by name
        for patent in relevant_patent_list:
            cleaned_reg_no = clean_reg_no(patent['document_id'])
            location_id = f"name-{cleaned_reg_no}"
            ip_manager.record_finding('patent', location_id, [cleaned_reg_no])

    langfuse.get_client().update_current_span(
        input={
            "CaseIndex": index,
            "PlaintiffName": plaintiff_name if 'plaintiff_name' in locals() else 'N/A',
            "ComplaintPDFPathFound": True if 'complaint_pdf_path' in locals() and complaint_pdf_path else False
        },
        output={
            "LLMSearchCriteriaForName": names if 'names' in locals() else 'N/A',
            "SearchTermUsedUSPTO": search_term_used if 'search_term_used' in locals() else 'N/A',
            "InitialPatentsFoundByName": len(patent_list) if 'patent_list' in locals() else 0,
            "RelevantPatentsAfterLLMFilter": len(relevant_patent_list) if 'relevant_patent_list' in locals() else 0,
            "ProcessPDFsSuccess": success if 'success' in locals() else False,
            "PatentGoalMetAfter": ip_manager.is_goal_met('patent')
        }
    )
    return success



async def process_patents_pdfs_from_uspto(df, index, case_directory, patent_list):
    # Common helper function to process patents recevied from the USPTO API: extract images and update dataframe.
    success = False
    images_dir = os.path.join(case_directory, "images")
    os.makedirs(images_dir, exist_ok=True)
    
    # Phase 1: Fetch missing design page numbers concurrently
    tasks_get_design_pages = []
    patents_needing_design_pages_indices = [] 

    for i, patent_item in enumerate(patent_list):
        # Use .get() for safer access and check if it's None or an empty list
        if not patent_item.get('design_page_numbers'): 
            document_id = patent_item.get('document_id')
            if document_id:
                tasks_get_design_pages.append(get_design_page_numbers(document_id))
                patents_needing_design_pages_indices.append(i)
            else:
                log_message(f"Patent at index {i} missing document_id, cannot fetch design pages.", level="WARNING")

    if tasks_get_design_pages:
        log_message(f"Fetching design page numbers for {len(tasks_get_design_pages)} patents concurrently...", level="INFO")
        results_design_pages = await asyncio.gather(*tasks_get_design_pages, return_exceptions=True)
        log_message(f"Finished fetching design page numbers.", level="INFO")

        for i, result_idx in enumerate(patents_needing_design_pages_indices):
            if isinstance(results_design_pages[i], Exception):
                log_message(f"Error fetching design pages for patent {patent_list[result_idx].get('document_id')}: {results_design_pages[i]}", level="ERROR")
                patent_list[result_idx]['design_page_numbers'] = [] # Default to empty list on error
            else:
                patent_list[result_idx]['design_page_numbers'] = results_design_pages[i]

    # Prepare batch of image processing tasks
    save_pdf_page_tasks = []
    for patent in patent_list:
        # Extract images (certificate and design pages) from each page of a PDF, intelligently identifying design pages.
        document_id = patent['document_id']
        patent_dir = os.path.join(os.getcwd(), "Documents", "IP", "Patents", document_id)
        patent_images_dir = os.path.join(patent_dir, "images")
        pdf_path = os.path.join(patent_dir, f"{document_id}.pdf")
        
        # Ensure patent images directory exists
        os.makedirs(patent_images_dir, exist_ok=True)

        certificate_filename = None
        
        # Check if certificate image already exists in patent_images_dir
        certificate_filename = f"{document_id}_full.webp"
        certificate_path_in_patent_dir = os.path.join(patent_images_dir, certificate_filename)
        certificate_path_in_case_dir = os.path.join(images_dir, certificate_filename)
        
        # Open the PDF document if we need to extract images
        pdf_document = None
        
        # Process certificate (first page) if it doesn't exist
        if not os.path.exists(certificate_path_in_patent_dir):
            pdf_document = fitz.open(pdf_path, filetype="pdf")
            if pdf_document.page_count == 0:  # the file is corrupted
                continue
                    
            save_pdf_page_tasks.append((pdf_path, 1, certificate_path_in_patent_dir, [cv2.IMWRITE_WEBP_QUALITY, 80]))
        
        # Extract only design pages (not all pages after the first one)
        for i, page_num in enumerate(patent['design_page_numbers']):
            if isinstance(page_num, str):
                try:
                    page_num = int(page_num)
                    patent['design_page_numbers'][i] = page_num
                except ValueError: # Some utility patent have no design pages.
                    print(f"\033[91m ⚠️ Error converting page number to int: {page_num} for patent {document_id}\033[0m")
                    patent['design_page_numbers'].pop(i)
                    continue
                
            page_filename = f"{document_id}_page{page_num}.webp"
            page_path_in_patent_dir = os.path.join(patent_images_dir, page_filename)
            page_path_in_case_dir = os.path.join(images_dir, page_filename)
            
            # Check if design page image already exists
            if not os.path.exists(page_path_in_patent_dir):
                if pdf_document is None:
                    pdf_document = fitz.open(pdf_path, filetype="pdf")
                    if pdf_document.page_count == 0:  # the file is corrupted
                        continue
                        
                # Add page to batch processing list
                save_pdf_page_tasks.append((pdf_path, page_num, page_path_in_patent_dir, [cv2.IMWRITE_WEBP_QUALITY, 80]))
                    
            # Keep track of which patent this page belongs to
            df.at[index, 'images']['patents'][page_filename] = {
                'product_name': patent.get("text"),
                'applicant': patent.get("inventors"),
                'patent_number': patent.get("document_id"),
                'full_filename': [certificate_filename]
            }
            success = True
            
        # Close PDF document if it was opened
        if pdf_document:
            pdf_document.close()
    
    # Process all images in parallel
    if save_pdf_page_tasks:
        log_message(f"Processing {len(save_pdf_page_tasks)} patent images in parallel")
        OCRProcessor.save_pdf_pages_batch(save_pdf_page_tasks)
        
    # After batch processing, copy any newly created images from patent directory to the case image directory
    for patent in patent_list:
        document_id = patent['document_id']
        patent_images_dir = os.path.join(os.getcwd(), "Documents", "IP", "Patents", document_id, "images")
        
        # Copy certificate
        certificate_filename = f"{document_id}_full.webp"
        certificate_path_in_patent_dir = os.path.join(patent_images_dir, certificate_filename)
        certificate_path_in_case_dir = os.path.join(images_dir, certificate_filename)
        if os.path.exists(certificate_path_in_patent_dir) and not os.path.exists(certificate_path_in_case_dir):
            shutil.copy2(certificate_path_in_patent_dir, certificate_path_in_case_dir)
            
        # Copy design pages
        for page_num in patent['design_page_numbers']:
            page_filename = f"{document_id}_page{page_num}.webp"
            page_path_in_patent_dir = os.path.join(patent_images_dir, page_filename)
            page_path_in_case_dir = os.path.join(images_dir, page_filename)
            
            if os.path.exists(page_path_in_patent_dir) and not os.path.exists(page_path_in_case_dir):
                shutil.copy2(page_path_in_patent_dir, page_path_in_case_dir)
    
    print(f"✅✅ process_patents_pdfs_from_uspto: Processed {len(patent_list)} patents with {len(save_pdf_page_tasks)} images")
    
    return success