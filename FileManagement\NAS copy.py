import paramiko
import os
from Common.Constants import local_case_folder, nas_case_folder, local_plaintiff_folder, nas_plaintiff_folder
from logdata import log_message
import shutil
import subprocess
from Common.Constants import sanitize_name
import time

class NASConnection:
    def __init__(self):
        self.ssh = None
        self._connect()
        
    def _connect(self):
        """Establishes a new connection with retry logic"""
        absolute_ssh_key_path = os.path.abspath(os.path.join(".ssh", "id_rsa"))
        self.ssh = paramiko.SSHClient()
        self.ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        max_attempts = 5
        for attempt in range(max_attempts):
            try:
                self.ssh.connect(os.getenv("NAS_HOST"), 22, 
                               os.getenv("NAS_USERNAME"), 
                               key_filename=absolute_ssh_key_path)
                return
            except Exception as e:
                if attempt == max_attempts - 1:
                    raise
                wait_time = 2 ** attempt
                print(f"SSH connection failed: {e}. Retrying in {wait_time:.1f} seconds...")
                time.sleep(wait_time)
    
    def ensure_connection(self):
        """Verifies and re-establishes connection if needed"""
        if not self.ssh or not self.ssh.get_transport().is_active():
            print("Connection lost, reconnecting...")
            self._connect()

    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.ssh:
            self.ssh.close()
            
    def close(self):
        """Closes the SSH connection."""
        if self.ssh:
            self.ssh.close()
            self.ssh = None

    # Converted functions as class methods
    def _zip_remote_folder(self, remote_folder, file_type=None):
        """Zips a remote folder on the NAS."""
        zip_file_name = f"{os.path.basename(os.path.normpath(remote_folder))}.zip"
        remote_zip_path = f"{nas_case_folder}/{zip_file_name}"
        print(f"Starting zipping of {remote_folder} to {remote_zip_path}")
        

        if self.ssh_exists(remote_zip_path):
            self.ssh_remove_file(remote_zip_path)

        if file_type:
            command = f'cd "/volume1{remote_folder}" && 7z a "/volume1{remote_zip_path}" {file_type} -r -x!"@eaDir" -y'
        else:
            command = f'cd "/volume1{remote_folder}" && 7z a "/volume1{remote_zip_path}" "*" -r -x!"@eaDir" -y'

        print(f"Executing command: {command}")
        self.ssh_execute_command(command) # Use the retrying function
        return remote_zip_path

    def send_files_to_nas(self, df_with_id, plaintiff_df):
        """Main method for sending files to NAS"""
        self.ensure_connection()
        try:
            for counter, (index, row) in enumerate(df_with_id.iterrows()):
                log_message(f'{counter+1}/{len(df_with_id)}: Sending case files for {row["docket"]} to NAS')
                
                # Send the case folder to NAS
                sanitized_docket = sanitize_name(f'{row["date_filed"].strftime("%Y-%m-%d")} - {row["docket"]}')
                case_directory_nas = f"{nas_case_folder}/{sanitized_docket}"
                case_directory_local = os.path.join(local_case_folder, sanitized_docket)
                self.ssh_local_to_nas(case_directory_local, case_directory_nas)
                
                # Send the plaintiff picture to NAS
                # ! If there was no picture extracted from the PDF, there are no new picture in the plaintiff folder
                if os.path.exists(os.path.join(case_directory_local, "images")) and len(os.listdir(os.path.join(case_directory_local, "images"))) > 0: 
                    plaintiff_name = plaintiff_df[plaintiff_df['id'] == row["plaintiff_id"]]['plaintiff_name'].values[0]
                    case_plaintiff_directory_nas = f"{nas_plaintiff_folder}/{sanitize_name(plaintiff_name)}"
                    self.copy_within_nas(f"{case_directory_nas}/images", case_plaintiff_directory_nas) # Copy the images folder
                else:
                    print(f'No new picture in the plaintiff folder for {row["docket"]}')
            
            print("NAS: Folder Zip file transfer completed successfully.")

        except Exception as e:
            print(f"send_files_to_nas: An error occurred: {e}")
            raise

    def ssh_local_to_nas(self, local_folder, remote_folder):
        """Transfers a local folder to NAS with connection handling"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                self.ensure_connection()
                zip_file_name = f"{os.path.basename(os.path.normpath(local_folder))}"
                local_zip_path = os.path.join(local_case_folder, zip_file_name)
                shutil.make_archive(local_zip_path, 'zip', local_folder)
                local_zip_path += '.zip'
                remote_zip_path = f"{nas_case_folder}/{zip_file_name}.zip"

                # if not os.path.exists(local_zip_path):
                #     print(f"\033[91mError: File {local_zip_path} was not found on local after transfer\033[0m")
                #     time.sleep(1)
                #     if not os.path.exists(local_zip_path):
                #         print(f"\033[91mError: Still not there after 1 second\033[0m")
                #     else:
                #         print(f"\033[92mFile {local_zip_path} found on local after transfer\033[0m")

                self.transfer_file_with_scp(local_path=local_zip_path, remote_path=remote_zip_path, to_nas=True)

                # Verify the file exists before trying to extract it
                # if not self.ssh_exists(remote_zip_path):
                #     print(f"\033[91mError: File {remote_zip_path} was not found on NAS after transfer -> Waiting 1 second\033[0m")
                #     time.sleep(1)
                #     if not self.ssh_exists(remote_zip_path):
                #         print(f"\033[91mError: File {remote_zip_path} was not found on NAS after Waiting -> Retrying\033[0m")
                #         self.transfer_file_with_scp(local_path=local_zip_path, remote_path=remote_zip_path, to_nas=True)
                #         if not self.ssh_exists(remote_zip_path):
                #             print(f"\033[91mError: File {remote_zip_path} was not found on NAS after Retrying\033[0m")
                #         else:
                #             print(f"\033[92mFile {remote_zip_path} found on NAS after Retrying\033[0m")
                #     else:
                #         print(f"\033[92mFile {remote_zip_path} found on NAS after Waiting 1 second\033[0m")

                command = f'7z x "/volume1{remote_zip_path}" "-o/volume1{remote_folder}" -y'
                self.ssh_execute_command(command)

                self.ssh_remove_file(remote_zip_path)
                os.remove(local_zip_path)
                return  # Exit the function if successful

            except Exception as e:
                print(f"\033[91mAttempt {attempt + 1}/{max_retries} failed: {e}\033[0m")
                time.sleep(2)  # Wait before retrying
        else:
            # If all retries failed
            raise Exception(f"Failed to transfer and unzip after {max_retries} attempts")

    def transfer_file_with_scp(self, local_path, remote_path, to_nas=True):
        """SCP file transfer with automatic connection handling and retries"""
        self.ensure_connection()
        absolute_ssh_key_path = os.path.abspath(os.path.join(".ssh", "id_rsa"))
        
        # Improved path handling for NAS
        remote_path = f"/volume1{remote_path}" if os.name != 'nt' else remote_path
        nas_path = f'{os.getenv("NAS_USERNAME")}@{os.getenv("NAS_HOST")}:{remote_path}'
        
        # Escape spaces in paths
        if os.name != 'nt':
            nas_path = nas_path.replace(' ', '\\ ')

        max_retries = 3
        retry_delay = 5  # seconds
        
        for attempt in range(max_retries):
            try:
                command = ["scp", "-r", "-i", absolute_ssh_key_path, "-P", "22",
                          "-o", "StrictHostKeyChecking=no",
                          "-o", "UserKnownHostsFile=/dev/null",
                          local_path if to_nas else nas_path,
                          nas_path if to_nas else local_path
                         ]
                result = subprocess.run(
                    command,
                    check=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                return  # Success
            except subprocess.CalledProcessError as e:
                error_msg = f"SCP attempt {attempt + 1} failed: {e.stderr}"
                if attempt < max_retries - 1:
                    error_msg += f" - Retrying in {retry_delay:.1f} seconds..."
                    print(error_msg)
                    time.sleep(retry_delay)
                else:
                    raise RuntimeError(f"SCP failed after {max_retries} attempts. Final error: {e.stderr}") from e


    def ssh_remove_file(self, remote_path):
        self.ssh_execute_command(f'rm "/volume1{remote_path}"') # remove expected return value, since we do not use it and it is now a tuple
        return # added return


    def ssh_exists(self, path):
        stdout, exit_code = self.ssh_execute_command(f'test -e "/volume1{path}"', expected_exit_codes=[0, 1])
        return exit_code == 0


    def download_file_from_nas(self, remote_path, local_path):
        """Downloads a file from NAS to local storage"""
        self.ensure_connection()
        try:
            self.transfer_file_with_scp(
                local_path=local_path,
                remote_path=remote_path,
                to_nas=False
            )
            print(f"Downloaded {remote_path} to {local_path}")
        except Exception as e:
            print(f"Download failed: {e}")
            raise

    def create_remote_directory(self, path):
        """Creates a directory on the NAS if it doesn't exist"""
        if not self.ssh_exists(path):
            self.ssh_execute_command(f'mkdir -p "/volume1{path}"') # remove expected return value, since we do not use it and it is now a tuple
            return # added return

    def list_remote_directory(self, path):
        """Lists contents of a remote directory"""
        stdout, _ = self.ssh_execute_command(f'ls -1 "/volume1{path}"') # now returns a tuple
        return stdout # return the stdout

    # copy all local folder that are missing on the NAS to the NAS
    def sync_local_to_nas(self):
        """Main synchronization method"""
        self.ensure_connection()
        for case_name in os.listdir(local_case_folder):
            local_folder = os.path.join(local_case_folder, case_name)
            if os.path.isdir(local_folder):
                nb_file = len(os.listdir(local_folder))
                if nb_file > 0:
                    remote_folder = f"{nas_case_folder}/{case_name}"
                    if not self.ssh_exists(remote_folder):
                        print(f"Folder {case_name} does not exist, copying it over")
                        self.ssh_local_to_nas(local_folder, remote_folder)
                else:
                    print(f"Folder {case_name} has only no file, skipping")
        print("Synchronization completed successfully")

    def ssh_nas_to_local(self, remote_folder, local_folder, file_type=None):
        """Transfers a folder from NAS to local by zipping remotely"""
        self.ensure_connection()
        try:
            remote_zip_path = self._zip_remote_folder(remote_folder, file_type)
            zip_file_name = os.path.basename(remote_zip_path)
            local_zip_path = os.path.join(local_case_folder, zip_file_name)
            
            self.transfer_file_with_scp(local_path=local_zip_path, remote_path=remote_zip_path, to_nas=False)
            
            self._unzip_local_file(local_zip_path, local_folder)
            os.remove(local_zip_path)
            self.ssh_remove_file(remote_zip_path)
            return True
        except Exception as e:
            print(f"ssh_nas_to_local error: {e}")
            return False

    def ssh_nas_to_local_aggregated(self, remote_folders, local_base_folder, file_type=None):
        """
        Transfers multiple folders from NAS to local by creating a single 7z archive.
        Uses the 7z command which is known to work on the target NAS.

        Args:
            remote_folders: A list of remote folder paths to transfer.
            local_base_folder: The local base folder where the unzipped content will be placed.
            file_type: Optional file type to include when zipping (e.g., "*.pdf").
        """
        self.ensure_connection()
        try:
            # Construct the zip command with multiple input folders
            zip_file_name = f"aggregated_{int(time.time())}.zip"
            remote_zip_path = f"{nas_case_folder}/{zip_file_name}"  # Store directly in /tmp
            local_zip_path = os.path.join(local_base_folder, zip_file_name)

            # Build the zip command.  Crucially, we *don't* cd into a directory first.
            # We provide the full paths to each folder to be included in the zip.
            zip_command = f'7z a "/volume1{remote_zip_path}" '
            for folder in remote_folders:
                zip_command += f'"/volume1{folder}" '  # Quote the folder paths

            zip_command += '-r -x!"@eaDir" -y'

            # if file_type:
            #     zip_command += f'-i "{file_type}"' # include the filetype
            
            print(f"Executing zip command: {zip_command}")
            self.ssh_execute_command(zip_command)

            # Transfer the zip file
            print(f"Transferring zip file to local: {local_zip_path}")
            self.transfer_file_with_scp(local_path=local_zip_path, remote_path=remote_zip_path, to_nas=False)

            # Unzip the local file
            print(f"Unzipping local file: {local_zip_path}")
            self._unzip_local_file(local_zip_path, local_base_folder)

            # Clean up: remove local and remote zip files
            print(f"Removing local zip file: {local_zip_path}")
            os.remove(local_zip_path)
            self.ssh_remove_file(remote_zip_path)
            return True

        except Exception as e:
            print(f"ssh_nas_to_local_aggregated error: {e}")
            return False
        

    def _unzip_local_file(self, local_zip_path, local_folder):
        """Helper method to unzip locally"""
        try:
            shutil.unpack_archive(local_zip_path, local_folder)
        except Exception as e:
            print(f"_unzip_local_file error: {e}")
            raise

    def ssh_remove_directory(self, path):
        """Recursively delete a directory on NAS"""
        self.ssh_execute_command(f'find "/volume1{path}" -mindepth 0 -delete') # remove expected return value, since we do not use it and it is now a tuple
        return # added return

    def ssh_get_folder_files_names(self, path, file_extension=None):
        """List all files in a remote directory, optionally filtered by extension"""
        base_cmd = f'find "/volume1{path}" -type f'
        if file_extension:
            base_cmd += f' -iname "{file_extension}"'
        stdout, _ = self.ssh_execute_command(base_cmd)
        return stdout

    def ssh_is_dir(self, path):
        """Check if remote path is a directory"""
        stdout, exit_code = self.ssh_execute_command(f'test -d "/volume1{path}"', expected_exit_codes=[0, 1])
        return exit_code == 0

    def ssh_execute_command(self, command, expected_exit_codes=[0]):
        """Executes a command on the NAS with retries.

        Args:
            command: The command to execute.
            expected_exit_codes: A list of exit codes that are considered
                successful. Defaults to [0].

        Returns:
            A tuple: (stdout, exit_status).  `stdout` is the standard output
            of the command as a string. `exit_status` is the integer exit
            status code.

        Raises:
            Exception: If the command fails after multiple retries, or if a
                non-recoverable SSH error occurs.
        """
        max_retries = 3
        retry_delay = 5  # seconds

        for attempt in range(max_retries):
            self.ensure_connection()  # Ensure connection *before* each attempt
            try:
                # Set a timeout for the command execution
                stdin, stdout, stderr = self.ssh.exec_command(command, timeout=60)

                # Read stdout *before* waiting for the exit status.
                stdout_str = stdout.read().decode()
                exit_status = stdout.channel.recv_exit_status()

                if exit_status in expected_exit_codes:
                    return stdout_str, exit_status  # Success!

                error_message = stderr.read().decode()
                # Retry logic: Only retry if NOT in expected_exit_codes
                if attempt < max_retries - 1:
                    print(f"🔴 Command failed (attempt {attempt + 1}): {error_message}. Retrying in {retry_delay:.1f} seconds...")
                    time.sleep(retry_delay)
                else:
                    raise Exception(f"Command failed after {max_retries} attempts: {error_message}")

            except paramiko.ssh_exception.SSHException as e:
                # Handle SSH connection errors (retriable)
                if attempt < max_retries - 1:
                    print(f"🔴 SSH connection error (attempt {attempt+1}): {e}. Retrying in {retry_delay:.1f} seconds...")
                    time.sleep(retry_delay)
                else:
                    raise Exception(f"🔴 SSH connection failed after {max_retries} attempts: {e}")

            except TimeoutError as e:
                # Handle command timeouts (retriable)
                if attempt < max_retries - 1:
                    print(f"🔴 Command timed out (attempt {attempt + 1}). Retrying in {retry_delay:.1f} seconds...")
                    time.sleep(retry_delay)
                else:
                    raise Exception(f"🔴 Command timed out after {max_retries} attempts.") from e

            except Exception as e:  # Other exceptions
                if attempt < max_retries - 1:
                    print(f"🔴 Command Unexpected failure (attempt {attempt + 1}): {e}. Retrying in {retry_delay:.1f} seconds...")
                    time.sleep(retry_delay)
                else:
                    raise Exception(f"🔴 Command Unexpected failure after {max_retries} attempts: {e}") from e


    def copy_within_nas(self, source_path, destination_path):
        """Copies files from one directory to another within the NAS."""
        self.ensure_connection()
        try:
            # Create the destination directory if it doesn't exist
            command = f'mkdir -p "/volume1{destination_path}"'
            self.ssh_execute_command(command) # No need to check the return

            # Copy files using 'cp -r' (recursive copy)
            command = f'cp -r "/volume1{source_path}"/* "/volume1{destination_path}"/'  # Copy all files and directories within source_path
            self.ssh_execute_command(command) # No need to check the return

            print(f"NAS: Files copied from {source_path} to {destination_path}")

        except Exception as e:
            print(f"copy_within_nas: An error occurred: {e}")
            raise



def test_transfer_speed(df, nas, type=None):
    
    """
    Tests and compares the speed of ssh_nas_to_local and ssh_nas_to_local_aggregated.
    """
    import time
    import shutil
    import pandas as pd

    num_runs = 5
    case_names = []
    case_directories_nas = []
    case_directories_local = []

    # Prepare test folders (create dummy folders on NAS if they don't exist)
    found_cases = 0
    for i, row in df.iterrows():
        if found_cases >= num_runs:
            break
        case_name = sanitize_name(f"{pd.to_datetime(row['date_filed'], errors='coerce').strftime('%Y-%m-%d')} - {row['docket']}")
        case_directory_nas = f"{nas_case_folder}/{case_name}"
        
        # Create dummy folder structure on NAS if it doesn't exist
        if nas.ssh_exists(case_directory_nas):
            case_directory_local = os.path.join(local_case_folder, case_name)
            case_names.append(case_name)
            case_directories_nas.append(case_directory_nas)
            case_directories_local.append(case_directory_local)
            found_cases += 1

    # --- Test ssh_nas_to_local ---
    print("Testing ssh_nas_to_local:")
    total_time_individual = 0
    for i in range(num_runs):
        # Clean up local directory before each run
        if os.path.exists(case_directories_local[i]):
            shutil.rmtree(case_directories_local[i])

        start_time = time.time()
        nas.ssh_nas_to_local(case_directories_nas[i], case_directories_local[i], type)
        end_time = time.time()
        run_time = end_time - start_time
        total_time_individual += run_time
        print(f"  Run {i+1}: {run_time:.4f} seconds")

    print(f"Total time (individual): {total_time_individual:.4f} seconds")

    # --- Test ssh_nas_to_local_aggregated ---
    print("\nTesting ssh_nas_to_local_aggregated:")

    # Clean up local base folder
    if os.path.exists(local_case_folder):
        for case_name in case_names:
            local_path = os.path.join(local_case_folder, case_name)
            if os.path.exists(local_path):
                shutil.rmtree(local_path)


    start_time = time.time()
    nas.ssh_nas_to_local_aggregated(case_directories_nas, local_case_folder, type)
    end_time = time.time()
    aggregated_time = end_time - start_time
    print(f"  Aggregated Time: {aggregated_time:.4f} seconds")



if __name__ == "__main__":
    with NASConnection() as nas:
        from DatabaseManagement.ImportExport import get_table_from_GZ
        df = get_table_from_GZ("tb_case", force_refresh=False)
        test_transfer_speed(df, nas, "*.pdf")
    
    with NASConnection() as nas:
        nas.sync_local_to_nas()
