# Cloudflare Workers for Google API Access in China

This folder contains four different Cloudflare Worker implementations to help bypass the Great Firewall of China (GFW) for Google API access. Each implementation serves a different approach depending on your needs.

## Workers Overview

### API Relay (Targeted Approach)

These workers only forward specific Google API requests:

1. **`google_api_relay_python.py`**: Python implementation for Cloudflare Workers
2. **`google_api_relay_js.js`**: JavaScript implementation

The API relay only forwards specific Google API endpoints and is optimized for Google Vertex AI and Gemini API usage.

### Full Proxy (General Approach)

These workers act as general HTTPS proxies:

1. **`full_proxy_python.py`**: Python implementation for Cloudflare Workers
2. **`full_proxy_js.js`**: JavaScript implementation

The full proxies can forward any HTTPS traffic, with special handling for Google API requests.

## How to Deploy

### 1. Set Up Cloudflare Workers

1. Sign up for a [Cloudflare Workers](https://workers.cloudflare.com/) account
2. Install Wrangler CLI: `npm install -g wrangler`
3. Authenticate with <PERSON>flare: `wrangler login`

### 2. Deploy a Worker

#### For JavaScript Workers:

1. Create a new worker project:
   ```
   wrangler init my-worker
   ```
2. Replace the generated `src/index.js` with the contents of your chosen JS worker file
3. Deploy the worker:
   ```
   wrangler publish
   ```

#### For Python Workers:

1. Create a new worker project for Python:
   ```
   wrangler init my-worker --type=python
   ```
2. Replace the generated worker code with the contents of your chosen Python worker file
3. Deploy the worker:
   ```
   wrangler publish
   ```

## How to Use

### API Relay Workers

These workers expect requests in the format:
```
https://your-worker.example.workers.dev/google-api/PATH
```

Where `PATH` is the Google API endpoint path.

Example code modification in `GC_Credentials.py`:

```python
def get_gcs_client_multi_project(current_project_index=None):
    # ... existing code ...
    
    try:
        # Add proxy for Google API requests
        proxy_url = "https://your-worker.example.workers.dev/google-api"
        
        # Modify URL construction for token endpoint
        token_url = f"{proxy_url}/token" 
        
        # Continue with existing code but route through proxy
        # ...
```

### Full Proxy Workers

These workers expect requests in the format:
```
https://your-worker.example.workers.dev/https://target-site.com/path
```

Example code modification in `GC_Credentials.py`:

```python
def get_gcs_client_multi_project(current_project_index=None):
    # ... existing code ...
    
    # Set up HTTP/HTTPS proxies
    os.environ['HTTPS_PROXY'] = 'https://your-worker.example.workers.dev/'
    
    # ... continue with existing code ...
```

## Features

- **IP Rotation**: Workers use Cloudflare's infrastructure to rotate outbound IPs when available
- **Retry Logic**: Built-in exponential backoff retry mechanism for reliability
- **Error Handling**: Graceful error responses when proxying fails
- **Headers Management**: Proper handling of HTTP headers in both directions

## Notes

- The full proxy is more versatile but may have higher latency for non-Google requests
- The API relay is more efficient but only works for Google API endpoints
- Consider using multiple Cloudflare worker instances with different domains for better reliability

## Security Considerations

- These workers should be deployed with appropriate access controls
- For production use, consider adding authentication to prevent abuse
- Regularly monitor your Cloudflare worker usage to stay within limits 