/**
 * <PERSON>flare Worker for Full HTTPS Proxy (JavaScript)
 * This worker acts as a general proxy for all HTTPS traffic, useful for bypassing China's Great Firewall
 */

addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request));
});

/**
 * Fetch with retry logic for reliability
 * @param {Request} request - Original request
 * @param {string} url - URL to fetch
 * @param {number} retryCount - Number of retry attempts
 * @param {number} retryDelay - Initial delay in ms between retries (increases exponentially)
 */
async function fetchWithRetry(request, url, retryCount = 3, retryDelay = 1000) {
  // Clone the request to modify
  const requestInit = {
    method: request.method,
    redirect: 'follow',
  };
  
  // Add body if applicable
  if (request.method !== 'GET' && request.method !== 'HEAD') {
    requestInit.body = await request.clone().arrayBuffer();
  }
  
  // Clone headers, removing those that shouldn't be forwarded
  const headers = new Headers();
  for (const [key, value] of request.headers.entries()) {
    if (!['host', 'cf-connecting-ip', 'cf-ipcountry', 'content-length', 'connection'].includes(key.toLowerCase())) {
      headers.set(key, value);
    }
  }
  
  // Set proper host header for the destination
  const targetHost = new URL(url).hostname;
  headers.set('Host', targetHost);
  
  // Add headers to request
  requestInit.headers = headers;
  
  console.log(`Proxying request to: ${url}`);
  console.log(`Request method: ${request.method}`);
  console.log(`Request headers: ${JSON.stringify(Object.fromEntries([...headers]))}`);
  
  let lastError;
  let lastResponse;
  for (let attempt = 0; attempt < retryCount; attempt++) {
    try {
      console.log(`Fetching ${url} (attempt ${attempt + 1}/${retryCount})`);
      const response = await fetch(url, requestInit);
      
      // If not successful, log the status and response
      if (!response.ok) {
        console.error(`Server responded with status: ${response.status}`);
        const responseText = await response.clone().text();
        console.error(`Response body: ${responseText.substring(0, 200)}`);
      } else {
        console.log(`Success! Status: ${response.status}`);
      }
      
      lastResponse = response;
      return response;
    } catch (error) {
      lastError = error;
      console.error(`Fetch error: ${error.message}`);
      if (attempt < retryCount - 1) {
        // Wait before retrying with exponential backoff
        await new Promise(resolve => setTimeout(resolve, retryDelay * (2 ** attempt)));
      }
    }
  }
  
  // If all retries fail but we have a response, return it anyway
  if (lastResponse) {
    return lastResponse;
  }
  
  // If all retries fail with no response, return an error
  return new Response(
    JSON.stringify({ error: `Failed after ${retryCount} attempts: ${lastError ? lastError.message : 'Unknown error'}` }),
    { 
      headers: { 'Content-Type': 'application/json' },
      status: 500
    }
  );
}

/**
 * Main request handler for proxy functionality
 * @param {Request} request - Original request
 */
async function handleRequest(request) {
  // Handle CORS preflight
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, PUT, POST, DELETE, HEAD, OPTIONS',
        'Access-Control-Allow-Headers': '*',
        'Access-Control-Max-Age': '86400'
      }
    });
  }

  // Parse the request URL and get the proxy target from the path
  const url = new URL(request.url);
  
  // The URL format should be: https://your-worker.domain.workers.dev/https://target-site.com/path
  // Extract the target URL from the path
  let targetUrl;
  
  if (url.pathname.startsWith('/https://')) {
    targetUrl = url.pathname.substring(1); // Remove leading slash
  } else if (url.pathname.startsWith('/http://')) {
    targetUrl = url.pathname.substring(1); // Remove leading slash
  } else {
    const match = url.pathname.match(/^\/(?:https?:\/\/)?(.*)/);
    if (!match) {
      return new Response("Invalid proxy request. Use format: /https://example.com/path", { status: 400 });
    }
    targetUrl = `https://${match[1]}`;
  }
  
  // Add query parameters
  if (url.search) {
    targetUrl += url.search;
  }
  
  console.log(`Full target URL: ${targetUrl}`);
  
  // Forward the request to the target URL
  const response = await fetchWithRetry(request, targetUrl);
  
  // Forward the response back to the client
  const responseHeaders = new Headers();
  for (const [key, value] of response.headers.entries()) {
    // Omit hop-by-hop headers
    if (!['connection', 'keep-alive', 'proxy-authenticate', 'proxy-authorization', 
         'te', 'trailer', 'transfer-encoding', 'upgrade'].includes(key.toLowerCase())) {
      responseHeaders.set(key, value);
    }
  }
  
  // Ensure CORS headers are set for browser requests
  responseHeaders.set('Access-Control-Allow-Origin', '*');
  responseHeaders.set('Access-Control-Allow-Methods', 'GET, PUT, POST, DELETE, HEAD, OPTIONS');
  responseHeaders.set('Access-Control-Allow-Headers', '*');
  
  // Create new response with the target site's content
  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: responseHeaders
  });
} 