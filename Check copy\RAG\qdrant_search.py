"""
Unified Qdrant-based implementation for IP asset similarity search.
This module provides functions to find similar IP assets using Qdrant vector database.
"""

import os
import aiohttp
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv
from langfuse import observe

# Import the embedding functions from RAG_Inference
from Check.RAG.RAG_Inference import get_siglip_embeddings

# Load environment variables
load_dotenv(os.path.join(os.getcwd(), "Qdrant", ".env"))

# Get Qdrant API details from environment variables
QDRANT_API_URL = os.getenv("QDRANT_API_URL")
QDRANT_API_KEY = os.getenv("QDRANT_API_KEY")

@observe()
async def find_similar_assets_qdrant(
    query_image_paths: List[str],
    check_id: str,
    client_id: str,
    ip_type: str,
    plaintiff_df=None,
    plaintiff_id: Optional[str] = None,
    top_n: int = 1,
    similarity_threshold: float = 0.8,
    similarity_threshold_text: float = 0.25
) -> List[Dict[str, Any]]:
    """
    Find similar IP assets using Qdrant vector database.
    
    Args:
        query_image_paths (List[str]): Paths to the query images
        check_id (str): The identifier for this specific check/batch
        client_id (str): The identifier for the client submitting the batch
        ip_type (str): The type of IP assets to search for ("Copyright", "Patent", "Trademark")
        plaintiff_df: DataFrame containing plaintiff information
        plaintiff_id (str, optional): Optional plaintiff ID to filter results
        top_n (int): Number of top similar images to return
        similarity_threshold (float): Minimum cosine similarity score to consider a match for images
        similarity_threshold_text (float): Minimum cosine similarity score to consider a match for text
        
    Returns:
        List[Dict[str, Any]]: List of match information for the top matches
    """
    if len(query_image_paths) == 0:
        return []

    # Generate embeddings for query images using SigLIP
    siglip_embeddings = get_siglip_embeddings(query_image_paths, "image")

    # Prepare the request payload for the forward_check endpoint
    products = []
    for i, path in enumerate(query_image_paths):
        products.append({
            "siglip_vector": siglip_embeddings[i].tolist(),
            "filename": os.path.basename(path)
        })
    
    payload = {
        "client_id": client_id,
        "check_id": check_id,
        "products": products,
        "search_ip_type": ip_type,
        "threshold": similarity_threshold*2-1,
        "threshold_text": similarity_threshold_text,
        "plaintiff_id": plaintiff_id,
        "top_n": top_n
    }
    
    # Make the API request to the forward_check endpoint
    async with aiohttp.ClientSession() as session:
        async with session.post(
            f"{QDRANT_API_URL}/forward_check",
            json=payload,
            headers={
                "Authorization": f"Bearer {QDRANT_API_KEY}",
                "Content-Type": "application/json"
            }
        ) as response:
            if response.status != 200:
                print(f"Error from Qdrant API: {response.status}")
                return []
            
            result = await response.json()
    
    # Process the results
    all_matches = []
    
    for product_result in result.get("results", []):
        product_filename = product_result.get("input_product_filename")
        query_image_path = next((path for path in query_image_paths if os.path.basename(path) == product_filename), None)
        
        if not query_image_path:
            continue
        
        for infringement in product_result.get("potential_infringements", []):
            if infringement.get("ip_type") != ip_type:
                continue
            
            metadata = infringement.get("metadata", {})
            
            if "plaintiff_id" in metadata: # TRO infringement
                result_plaintiff_id = metadata.get("plaintiff_id", [])
                plaintiff_name = plaintiff_df.loc[plaintiff_df['id'] == result_plaintiff_id, 'plaintiff_name'].iloc[0] if not plaintiff_df.loc[plaintiff_df['id'] == result_plaintiff_id].empty else ""
                
                match_info = {
                    "query_image_path": query_image_path,
                    "filename": metadata.get("reg_no", "") or metadata.get("filename", ""),
                    "full_filename": metadata.get("full_filename", "") or metadata.get("filename", ""),
                    "similarity": str(infringement.get("score", 0)/2+0.5),
                    'plaintiff_id': result_plaintiff_id,
                    "plaintiff_name": plaintiff_name,
                    "docket": metadata.get("docket", ""),
                    "number_of_cases": len(metadata.get("number_of_cases", 0)),
                    "reg_no": metadata.get("reg_no", ""), # Copyright & Trademark
                    "int_cls_list": metadata.get("int_cls", []),  # Trademark
                    "patent_number": metadata.get("patent_number", ""),  # Patent
                    "text": metadata.get("text", ""),  # Patent
                    # "full_patent": get_patent_pages(cases_df, plaintiff_df, single_match), # Patent
                    "data_type": metadata.get("data_type", "") # Patent: not used
                }
                
                # Add specific fields based on IP type
                if ip_type == "Trademark":
                    match_info["plaintiff_id"] = metadata.get("plaintiff_ids", [None])[0]
                
                all_matches.append(match_info)
                
            else: # Non-TRO infringement
                match_info = {
                    "query_image_path": query_image_path,
                    "metadata": metadata
                }
                all_matches.append(match_info)
    
    # Return top N results
    return all_matches
