#nofloqa
import asyncio
import os
import json
import re # For JSON extraction
from typing import List, Dict, Optional, Any

from AI.GC_VertexAI import vertex_genai_multi_async
import Common.Constants as Constants
from logdata import log_message
from langfuse import observe
from Alerts.IPTrackingManager import IPTrackingManager # Added import


def _extract_json_from_llm_response(response_text: str) -> Optional[List[Dict[str, str]]]:
    """
    Extracts a JSON list of objects from the LLM's response string.
    Tries to handle markdown code blocks or direct JSON.
    """
    if not response_text:
        log_message("    D_Map_LLM_Parse: LLM response was empty.", level="WARNING")
        return None
    
    response_text = response_text.strip()
    
    match = re.search(r"```json\s*([\s\S]*?)\s*```", response_text, re.IGNORECASE)
    if match:
        json_str = match.group(1).strip()
    elif response_text.startswith("[") and response_text.endswith("]"):
        json_str = response_text
    else:
        start_index = response_text.find('[')
        end_index = response_text.rfind(']')
        if start_index != -1 and end_index != -1 and end_index > start_index:
            json_str = response_text[start_index : end_index+1]
        else:
            log_message(f"    D_Map_LLM_Parse: Could not find JSON list structure. Response snippet: {response_text[:200]}", level="WARNING")
            return None

    try:
        parsed_json = json.loads(json_str)
        if isinstance(parsed_json, list):
            if not parsed_json: # Empty list is valid
                 return []
            if all(isinstance(item, dict) and "image_identifier" in item and "matched_usco_reg_no" in item for item in parsed_json):
                return parsed_json
            else:
                log_message(f"    D_Map_LLM_Parse: Parsed JSON list items do not have expected keys. Snippet: {str(parsed_json)[:200]}", level="WARNING")
                return None
        else:
            log_message(f"    D_Map_LLM_Parse: Parsed JSON is not a list. Type: {type(parsed_json)}. Snippet: {json_str[:200]}", level="WARNING")
            return None
    except json.JSONDecodeError as e:
        log_message(f"    D_Map_LLM_Parse: Failed to decode JSON: {e}. JSON string snippet: {json_str[:200]}", level="WARNING")
        return None

@observe()
async def D_map_reg_nos_to_images_async(
    d_step_input_items: List[Dict[str, Any]], # Combined list with all necessary info
    ip_manager: IPTrackingManager, # This is {std_reg_no: {title, claimant, reg_no}}
    docket: str
) -> List[Dict[str, Any]]:
    """
    Maps final processed copyright images to the best matching USCO registration number using an LLM.

    Args:
        d_step_input_items: A list of dictionaries. Each dictionary contains all context
                              for an image processed by C-step, including its
                              `processed_image_path_from_C` (path to send to LLM),
                              `reg_no` (original C-step input identifier),
                              `candidate_usco_reg_nos`, `img_url` (original from B), etc.
        master_copyright_details_from_usco: Dict of {std_reg_no: {title, claimant, reg_no}}.
        docket: The case docket number for context.
    Returns:
        A list of dictionaries, each containing details for a processed image:
        {"final_image_path": str, "matched_usco_reg_no": Optional[str], 
         "source_site": str, "original_image_url": str, "image_identifier_C": str}
    """
    # --- Construct LLM Prompt for all images ---
    llm_api_input_parts = []

    # Initial text part (instructions and USCO list)
    initial_prompt_text_parts = [
        f"You are an expert copyright analyst. You will be provided with a series of images and a comprehensive list of USCO copyright registration details relevant to case {docket}.\n"
        "For each image, I will give you an 'Image Identifier', its original filename from the website, and potentially a list of 'Suggested Candidate Reg Nos'.\n"
        "Your task is to analyze each image and select the single best matching USCO Registration Number from the 'Overall USCO Details List' provided below.\n"
        "If 'Suggested Candidate Reg Nos' are provided for an image, consider them, but you are not limited to them. Your final choice must come from the 'Overall USCO Details List'.\n"
        "If no registration number from the 'Overall USCO Details List' is a good match for an image, or if you are uncertain, you MUST return the string \"None\" for that image.\n\n"
        "Output your response as a single JSON list of objects. Each object in the list must have two keys:\n"
        "1. \"image_identifier\": The 'Image Identifier' I provided for that image (e.g., \"Image_1\").\n"
        "2. \"matched_usco_reg_no\": The selected USCO registration number (e.g., \"VA0001234567\") from the 'Overall USCO Details List', or the exact string \"None\".\n\n"
        "Ensure every image for which an 'Image Identifier' was provided in this prompt is represented in your JSON output.\n"
        "Do not include any text before or after the JSON list.\n\n"
        "--- Overall USCO Details List (Candidates for all images) ---\n"
    ]
    
    # First we update the ip_manage with the reg numbers we already have found. This is key for them not to be included in copyright_records_without_images
    for idx, d_input_item in enumerate(d_step_input_items):
        if bool(re.match(r"^[a-zA-Z]{2}\d{10}", os.path.basename(d_input_item["final_image_path"]))):
            d_input_item["matched_usco_reg_no"] = d_input_item["reg_no"]
            ip_manager.update_copyright_image_found(d_input_item["reg_no"], f"chinese_websites ({d_input_item['method']})")
    
    
    copyright_records_without_images = ip_manager.get_copyrights_without_images() 
    for i, row in copyright_records_without_images.iterrows():
        initial_prompt_text_parts.append(
            f"  USCO Detail {i + 1}: Reg No: {row['registration_number']}, "
            f"Title: {row['title']}, Claimant: {row['names']}\n"
        )
    
    initial_prompt_text_parts.append("\n--- Images for Analysis ---\n\n")
    llm_api_input_parts.append(("text", "".join(initial_prompt_text_parts)))

    # Interleave image-specific text and image data
    for idx, d_input_item in enumerate(d_step_input_items):
        if bool(re.match(r"^[a-zA-Z]{2}\d{10}", os.path.basename(d_input_item["final_image_path"]))):
            continue

        if not os.path.exists(d_input_item["final_image_path"] ):
            log_message(f"    D_Map_Prep: Image path {d_input_item["final_image_path"] } (ID: {d_input_item["reg_no"] }) does not exist. Skipping LLM mapping.", level="WARNING")
            continue
    
        sequential_llm_id = f"Image_{idx + 1}"
        d_input_item["sequential_llm_id"] = sequential_llm_id

        original_img_filename_for_prompt = os.path.basename(d_input_item.get('img_url', 'N/A')) if d_input_item.get('img_url', 'N/A') != 'N/A' else 'N/A'

        image_specific_text_parts = [
            f"--- {sequential_llm_id} ---\n",
            f"Image Identifier: {sequential_llm_id}\n",
            f"Original Filename from Website: {original_img_filename_for_prompt}\n"
        ]
        if d_input_item.get("candidate_usco_reg_nos", []):
            image_specific_text_parts.append(f"Suggested Candidate Reg Nos: {', '.join(d_input_item.get("candidate_usco_reg_nos", []))}\n")
        else:
            image_specific_text_parts.append("Suggested Candidate Reg Nos: None provided.\n")
        image_specific_text_parts.append("---\n\n")
        
        llm_api_input_parts.append(("text", "".join(image_specific_text_parts)))
        llm_api_input_parts.append(("image_path", d_input_item["final_image_path"])) # Full path for API
        
    
    llm_api_input_parts.append(("text", "Remember to provide your entire response as a single JSON list."))
    # --- End LLM Prompt Construction ---

    try:
        parsed_llm_results = {}
        if not copyright_records_without_images.empty and any("sequential_llm_id" in item for item in d_step_input_items):
            llm_response_str = await vertex_genai_multi_async(llm_api_input_parts, model_name=Constants.IMAGE_MODEL_FREE_LIMITED, useVertexAI=Constants.IMAGE_MODEL_FREE_LIMITED_VERTEX)
        
            if not llm_response_str:
                log_message("    D_Map: LLM returned an empty response. Marking all images as 'None'.", level="WARNING")
            else:
                parsed_llm_results = _extract_json_from_llm_response(llm_response_str)
                if parsed_llm_results is None:
                    log_message(f"    D_Map: Failed to parse JSON response from LLM. LLM Raw: '{llm_response_str[:300]}...'. Marking all images as 'None'.", level="WARNING")
        elif not any("sequential_llm_id" in item for item in d_step_input_items):
            log_message("    D_Map: No images eligible for LLM mapping after preparation.", level="INFO")
        elif copyright_records_without_images.empty:
            log_message("    D_Map: No copyright numbers without image to match against.", level="INFO")


        # Iterate through the original list of items we prepared for the LLM
        for d_item_data in d_step_input_items:
            if parsed_llm_results and "sequential_llm_id" in d_item_data:
                llm_answer_item = next((llm_item for llm_item in parsed_llm_results if llm_item["image_identifier"] == d_item_data["sequential_llm_id"]), None)
                
                if llm_answer_item:
                    llm_matched_reg_no_str = llm_answer_item["matched_usco_reg_no"] if llm_answer_item else None

                    if llm_matched_reg_no_str and isinstance(llm_matched_reg_no_str, str) and llm_matched_reg_no_str != "None" and llm_matched_reg_no_str in copyright_records_without_images['registration_number'].tolist():
                        d_item_data["matched_usco_reg_no"] = llm_matched_reg_no_str
                    
            if "matched_usco_reg_no" in d_item_data and d_item_data["matched_usco_reg_no"] and d_item_data["matched_usco_reg_no"] in ip_manager.copyright_dataframe['registration_number'].values:
                ip_manager.update_copyright_image_found(d_item_data["matched_usco_reg_no"], f"chinese_websites ({d_item_data['method']})")
                log_message(f"   We matched {os.path.basename(d_item_data['final_image_path'])} to {d_item_data["matched_usco_reg_no"]}", level="INFO")
            else:
                # Generate MD registration number for unknown copyright
                md_reg_no = ip_manager.generate_md_registration_number()
                d_item_data["matched_usco_reg_no"] = md_reg_no
                ip_manager.add_copyright_to_dataframe({'registration_number': md_reg_no, 'image_found': 1, 'image_source': f'chinese_websites ({d_item_data['method']})'})
                log_message(f"   Generated MD registration number {md_reg_no} for unknown copyright image", level="INFO")                
                
            # Rename the image using the reg number
            final_image_path_ext = os.path.splitext(d_item_data["final_image_path"])[1]
            new_filename = f"{d_item_data["matched_usco_reg_no"]}_{os.path.basename(d_item_data['method'])}.{final_image_path_ext[1:]}"
            new_image_path = os.path.join(os.path.dirname(d_item_data["final_image_path"]), new_filename)
            os.rename(d_item_data["final_image_path"], new_image_path)
            d_item_data["final_image_path"] = new_image_path
    
    except Exception as e:
        log_message(f"    D_Map: Error during LLM call or processing: {e}", level="ERROR")

    return d_step_input_items