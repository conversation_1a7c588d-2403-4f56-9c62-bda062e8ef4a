import os
from FileManagement.Tencent_COS import get_cos_client, download_from_cos, async_upload_file_with_retry, async_copy_file_with_retry
from AI.LLM_shared import get_json, get_report
from AI.GC_VertexAI import vertex_genai_multi_async
from Check.RAG.npy_patent import find_most_similar_patent_image
import json
from langfuse import observe
import langfuse
import time
import asyncio
from Check.Do_Check_Download import download_from_url

# @observe()
# @profile
async def check_one_patent(temp_dir, check_id, result, plaintiff_df, client, bucket, report_design_prompt, report_utility_prompt, query_image_urls):
    print(f"\033[94mPatent: Checking patent {result['plaintiff_name']}\033[0m")
    plaintiff_id = plaintiff_df.loc[plaintiff_df['plaintiff_name'] == result['plaintiff_name'], 'id'].iloc[0]

    report_prompt = report_design_prompt if "D" in result['patent_number'] else report_utility_prompt
    registered_patent = "Registered Design Patent" if "D" in result['patent_number'] else "Registered Utility Patent"
    
    prompt_list = [
        ("text", report_prompt),
        ("text", f'\n\nAfter the report, conclude with regards to the risk of infringement with {{"final_answer": "xx"}} where xx is a score between 0 and 10 where 0 is very low risk, and 10 is very high risk.'),
        ("text", "\n\nProduct Image:\n"),
        ("image_path", result["query_image_path"]),
        ("text", f"\n\n{registered_patent} with registration number {result['patent_number']}:\n")
    ]

    # Query_URL = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/checks/{check_id}/query/{os.path.basename(result['query_image_path'])}"
    Query_URL = query_image_urls[os.path.basename(result['query_image_path'])]
    image_url = ["", "", "", Query_URL.replace(" ", "%20").replace("http", "https"), ""]

    download_tasks = []
    for image_path in result["full_patent"]:
        ip_file_local = os.path.join(temp_dir, image_path)
        IP_Url = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{plaintiff_id}/high/{image_path}"
        download_tasks.append(download_from_url(IP_Url, ip_file_local))
        prompt_list.append(("image_path", ip_file_local))
        image_url.append(IP_Url.replace(" ", "%20").replace("http", "https"))

    download_results = await asyncio.gather(*download_tasks)

    for download_result, image_path in zip(download_results, result["full_patent"]):
        if not download_result:
            # Handle the error, log it to the error file
            IP_Url = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{plaintiff_id}/high/{image_path}"
            os.makedirs(os.path.join(os.getcwd(), "Errors"), exist_ok=True)
            with open(os.path.join(os.getcwd(), "Errors", "IP_Download_Error.txt"), "a") as f:
                f.write(f"{check_id} - {IP_Url}\n")
            return None

    ai_answer = await vertex_genai_multi_async(prompt_list, image_url=image_url, model_name="gemini-2.0-flash-exp")
    is_patent = get_json(ai_answer)

    try:
        final_answer_score = int(is_patent["final_answer"])
    except:
        final_answer_score = 0  # so for "Report not required" we get a score of 0

    if final_answer_score > 0:
        report = get_report(ai_answer, "**1. Patent Information:**", "**End of Report**")
        report = "**Patent Risk Assessment Report**\n\n" + report

        copy_tasks = [async_copy_file_with_retry(client, bucket, f"checks/{check_id}/results/{filename}", f"plaintiff_images/{plaintiff_id}/high/{filename}") for filename in result["full_patent"]]
        await asyncio.gather(*copy_tasks)
        # await upload_task

        return {
            "type": "patent",
            "ip_owner": str(result['plaintiff_name']),
            "plaintiff_id": str(plaintiff_id),
            "report": report,
            "risk_level": "高风险" if final_answer_score > 5 else "中风险" if final_answer_score > 2 else "低风险",
            "risk_description": "和其他产权有匹配，并且该产权或产权所有人曾经发起TRO诉讼",
            "ip_image": result['full_patent'],
            "Query_URL": Query_URL
        }
    else:
        # await upload_task
        return None

@observe()
async def check_patents(client, bucket, temp_dir, check_id, local_product_images, local_ip_images, local_reference_images, description, ip_keywords, reference_text, cases_df, plaintiff_df, query_image_urls, use_qdrant=False):

    start_time = time.time()

    if use_qdrant:
        # Use Qdrant for vector search
        from Check.RAG.qdrant_search import find_similar_assets_qdrant
        sim_results = await find_similar_assets_qdrant(
            query_image_paths=local_product_images+local_ip_images+local_reference_images,
            check_id=check_id,
            client_id=f"check_{check_id}",
            ip_type="Patent",
            plaintiff_df=plaintiff_df,
            plaintiff_id=None,
            top_n=5,
            similarity_threshold=0.6,
            similarity_threshold_text=0.25
        )
    else:
        # Use original vector search method
        sim_results = find_most_similar_patent_image(
            cases_df=cases_df,
            plaintiff_df=plaintiff_df,
            query_image_paths=local_product_images+local_ip_images+local_reference_images,
            top_n=5,
            similarity_threshold_images=0.6,
            similarity_threshold_text=0.25
        )

    print(f"\033[94mPatent: Patent RAG for {len(local_product_images)} pictures done in {time.time() - start_time:.1f} seconds\033[0m")

    if sim_results:
        with open(os.path.join(os.getcwd(), "Check", "Prompts", "Report_Patent_Design.txt"), "r", encoding="utf-8") as f:
            report_design_prompt = f.read()
        with open(os.path.join(os.getcwd(), "Check", "Prompts", "Report_Patent_Utility.txt"), "r", encoding="utf-8") as f:
            report_utility_prompt = f.read()

        # Concurrently process each patent check
        patent_check_tasks = [check_one_patent(temp_dir, check_id, result, plaintiff_df, client, bucket, report_design_prompt, report_utility_prompt, query_image_urls) for result in sim_results]
        patent_results = await asyncio.gather(*patent_check_tasks)

        # Filter out None results (where is_copyright["final_answer"] was not "yes")
        filtered_patent_results = [r for r in patent_results if r]
        print(f"\033[94m ✅ Patent: Patent Report Analysis DONE, for {len(sim_results)} results in {time.time() - start_time:.1f} seconds\033[0m")
        return filtered_patent_results # not used, but tracked by langfuse
    else:
        print(f"\033[94m ✅ Patent: RAG returned no pottential patent matches in {time.time() - start_time:.1f} seconds\033[0m")
        return []
