#!/usr/bin/env python3
"""
Test script to verify the directory structure logic matches between 
trademark_image.py and get_bulk_trademarks.py
"""

import os

def get_directory_structure_trademark_image(serial_number):
    """Logic from trademark_image.py"""
    if len(serial_number) >= 4:
        xx = serial_number[-2:]
        yy = serial_number[-4:-2]
    elif len(serial_number) > 0:
        xx = serial_number.zfill(2)[-2:]
        yy = "00"
    else:
        return ""
    return os.path.join(xx, yy)

def get_directory_structure_get_bulk_trademarks(ser_no):
    """Logic from get_bulk_trademarks.py (after optimization)"""
    ser_no_str = str(ser_no)
    if len(ser_no_str) >= 4:
        xx = ser_no_str[-2:]  # Last 2 digits
        yy = ser_no_str[-4:-2]  # 2 digits before the last 2
        image_sub_dir = os.path.join(xx, yy)
    elif len(ser_no_str) > 0:
        # Handle short serial numbers (same logic as trademark_image.py)
        xx = ser_no_str.zfill(2)[-2:]  # Pad with zeros if needed
        yy = "00"
        image_sub_dir = os.path.join(xx, yy)
    else:
        image_sub_dir = ""  # Should not happen, but defensive
    return image_sub_dir

def test_directory_structures():
    """Test various serial numbers to ensure both functions produce the same result"""
    test_cases = [
        "12345678",  # 8 digits
        "1234567",   # 7 digits
        "123456",    # 6 digits
        "12345",     # 5 digits
        "1234",      # 4 digits (minimum for normal logic)
        "123",       # 3 digits (short)
        "12",        # 2 digits (short)
        "1",         # 1 digit (short)
        "",          # Empty (edge case)
    ]
    
    print("Testing directory structure logic consistency:")
    print("Serial Number | trademark_image.py | get_bulk_trademarks.py | Match")
    print("-" * 70)
    
    all_match = True
    for serial in test_cases:
        dir1 = get_directory_structure_trademark_image(serial)
        dir2 = get_directory_structure_get_bulk_trademarks(serial)
        match = dir1 == dir2
        all_match = all_match and match
        
        print(f"{serial:12} | {dir1:18} | {dir2:21} | {'✅' if match else '❌'}")
    
    print("-" * 70)
    print(f"All tests {'PASSED' if all_match else 'FAILED'}")
    
    # Test some specific examples
    print("\nSpecific examples:")
    examples = ["87654321", "1234", "123", "12"]
    for example in examples:
        dir_structure = get_directory_structure_get_bulk_trademarks(example)
        print(f"Serial {example} -> Directory: {dir_structure}")

if __name__ == "__main__":
    test_directory_structures()
