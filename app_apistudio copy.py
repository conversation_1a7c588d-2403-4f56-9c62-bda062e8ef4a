from langfuse import Lang<PERSON>, observe
from langfuse._client.resource_manager import LangfuseResourceManager
import os
# ❌⚠️📥🔥✅☑️✔️💯💧⛏🔨🗝🔑 🔒💡⛔🚫❗


# Initialize Langfuse client
os.environ["LANGFUSE_SECRET_KEY"] = "******************************************"
os.environ["LANGFUSE_PUBLIC_KEY"] = "pk-lf-bac4b191-590d-4059-89d6-e5ff73e91b48"
langfuse_client = Langfuse(public_key="pk-lf-bac4b191-590d-4059-89d6-e5ff73e91b48", secret_key="******************************************") 
trade_id = langfuse_client.create_trace_id(seed="test_seed")
print(f"Trace ID: {trade_id}")



@observe
def process_check_with_db(check_id):
    print(f"Intances in process_check_with_db: {len(LangfuseResourceManager._instances)}")
    return check_id


@observe(name="/check_api")
def check_api():
    trace_id = langfuse_client.get_current_trace_id()
    print(f"Intances in check_api: {len(LangfuseResourceManager._instances)}")
    
    process_check_with_db(check_id="check_id", langfuse_trace_id=trace_id)
    
    return {
        'check_id': "check_id",
        'status': 'processing',
        'message': 'Analysis started. Use check_status endpoint to poll results.'
    }


# Standalone server
if __name__ == '__main__':
    import os
    os.environ["LANGFUSE_SECRET_KEY"] = "******************************************"
    os.environ["LANGFUSE_PUBLIC_KEY"] = "pk-lf-bac4b191-590d-4059-89d6-e5ff73e91b48"
    print(f"Intances in main: {len(LangfuseResourceManager._instances)}")
    

    
    print(check_api())