from Alerts.Chrome_Driver import get_driver # Import get_driver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementClickInterceptedException
import time
import os
import requests
import argparse
import uuid
from PIL import Image, ImageChops
from io import BytesIO
import random
import torch
from torchvision import models, transforms
from torchvision.models import MobileNet_V2_Weights
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
import shutil
import glob
import json
import re
from logdata import log_message
from langfuse import observe
import langfuse

# Constants for image similarity
SIMILARITY_THRESHOLD = 0.40

# List of websites to avoid
BLACKLISTED_DOMAINS = [
    "qqdip.com",
    "sellerguard.com.cn",
    "saibeiip.com",
    "maijiazhichi.com",
    "mooting.cn",
    "fangtion.com",
    "daxinfawu.com",
    "10100.com",
    "sellerdefense.cn"
]

def is_blacklisted_url(url):
    """Check if URL is from a blacklisted domain"""
    if not url:
        return False
        
    # Extract domain from URL
    try:
        # Remove protocol and get domain
        domain = re.sub(r'https?://', '', url.lower())
        domain = domain.split('/')[0]
        
        # Check against blacklisted domains
        for blacklisted_domain in BLACKLISTED_DOMAINS:
            if blacklisted_domain in domain:
                log_message(f"  ⚠️ Blacklisted domain detected: {domain} matches {blacklisted_domain}")
                return True
                
        return False
    except Exception as e:
        log_message(f"  ❌ Error checking domain: {e}")
        return False

def remove_white_background(image_path):
    """Remove extra white background around an image"""
    try:
        # First make sure the file exists and has content
        if not os.path.exists(image_path) or os.path.getsize(image_path) == 0:
            log_message(f"  ❌ Cannot process empty or non-existent file: {image_path}")
            return False
            
        # Open the image in RGB mode first to ensure it's a valid image
        try:
            original_img = Image.open(image_path)
            # Verify format
            if original_img.format not in ['JPEG', 'PNG', 'WEBP']:
                log_message(f"  ❌ Invalid image format for {image_path}: {original_img.format}")
                return False
        except Exception as e:
            log_message(f"  ❌ Cannot open image file {image_path}: {e}")
            return False
            
        # Make a copy of the original image for safety, ensuring a valid extension
        base, ext = os.path.splitext(image_path)
        temp_path = base + ".tempcopy" + ext # e.g., image.jpg -> image.tempcopy.jpg

        original_img.save(temp_path)
        
        # Try to open in RGBA
        try:
            img = Image.open(temp_path).convert('RGBA')
        except Exception as e:
            log_message(f"  ❌ Cannot convert image to RGBA: {e}")
            # Cleanup and keep original file
            if os.path.exists(temp_path):
                os.remove(temp_path)
            return False
        
        # Get the data
        data = img.getdata()
        
        # Create a new image with transparent background
        new_data = []
        for item in data:
            # If pixel is almost white (threshold can be adjusted)
            if item[0] > 245 and item[1] > 245 and item[2] > 245:
                # Make it transparent
                new_data.append((255, 255, 255, 0))
            else:
                new_data.append(item)
        
        # Update the image with transparent background
        img.putdata(new_data)
        
        # Trim the transparent edges
        bg = Image.new(img.mode, img.size, (0, 0, 0, 0))
        diff = ImageChops.difference(img, bg)
        bbox = diff.getbbox()
        
        if bbox:
            img = img.crop(bbox)
        
        # Save the trimmed image with proper format
        try:
            # Get file extension
            _, ext = os.path.splitext(image_path)
            if ext.lower() in ['.jpg', '.jpeg']:
                # Convert RGBA to RGB for JPEG
                img = img.convert('RGB')
                img.save(image_path, 'JPEG', quality=95)
            elif ext.lower() == '.png':
                img.save(image_path, 'PNG')
            else:
                # Default to JPEG
                img = img.convert('RGB')
                img.save(image_path, 'JPEG', quality=95)
                
            # log_message(f"  ✅ Removed white background from: {os.path.basename(image_path)}")
            
            # Clean up temp file
            if os.path.exists(temp_path):
                os.remove(temp_path)
                
            return True
        except Exception as e:
            log_message(f"  ❌ Failed to save processed image: {e}")
            # Restore original if we failed
            if os.path.exists(temp_path):
                shutil.copy2(temp_path, image_path)
                os.remove(temp_path)
            return False
            
    except Exception as e:
        log_message(f"  ❌ Error removing white background from {os.path.basename(image_path)}: {e}")
        return False

def extract_feature_vector(image_path):
    """Extract feature vector from image using MobileNetV2"""
    # First verify the image exists and has content
    if not os.path.exists(image_path):
        log_message(f"  ❌ Image file does not exist: {image_path}")
        return None
        
    if os.path.getsize(image_path) == 0:
        log_message(f"  ❌ Image file is empty: {image_path}")
        return None
    
    # Try to validate the image before processing
    try:
        with Image.open(image_path) as img:
            # Check if image is valid
            img.verify()
            if img.format not in ['JPEG', 'PNG', 'WEBP', 'BMP', 'GIF']:
                log_message(f"  ❌ Unsupported image format in {image_path}: {img.format}")
                return None
    except Exception as e:
        log_message(f"  ❌ Invalid image file {image_path}: {e}")
        return None
    
    # Load model with pre-trained weights
    weights = MobileNet_V2_Weights.DEFAULT
    model = models.mobilenet_v2(weights=weights).features.eval()
    preprocess = weights.transforms()
    
    try:
        # Open in RGB mode explicitly
        image = Image.open(image_path).convert('RGB')
        # Ensure the image has actual content
        if image.width <= 10 or image.height <= 10:
            log_message(f"  ❌ Image too small to process: {image_path}")
            return None
            
        tensor = preprocess(image).unsqueeze(0)
        with torch.no_grad():
            # features = model(tensor).squeeze().numpy()
            output_tensor = model(tensor)
            squeezed_tensor = output_tensor.squeeze()
            numpy_features = squeezed_tensor.numpy()
        return numpy_features.flatten()
    except Exception as e:
        log_message(f"  ❌ Error extracting features from {image_path}: {e}")
        return None

def compute_similarity(source_vector, target_vector):
    """Compute cosine similarity between two feature vectors"""
    if source_vector is None or target_vector is None:
        return 0
    return cosine_similarity([source_vector], [target_vector])[0][0]

def save_image_data_to_json(image_data, json_file):
    """Save image data to a JSON file"""
    try:
        # Load existing data if the file exists
        if os.path.exists(json_file):
            with open(json_file, 'r') as f:
                existing_data = json.load(f)
        else:
            existing_data = {}
        
        # Update with new data
        existing_data.update(image_data)
        
        # Save updated data
        with open(json_file, 'w') as f:
            json.dump(existing_data, f, indent=4)
            
        log_message(f"  ✅ Updated image data in {json_file.replace(os.getcwd(), '')}")
    except Exception as e:
        log_message(f"  ❌ Error saving image data to JSON: {e}")

@observe(capture_output=False)
def reverse_search_on_google(image_path, output_folder="similar_images", final_folder="final_selection", max_images=5):
    """
    Perform reverse image search on Google Images, download similar images,
    and filter based on similarity to the original image.
    Skips images from blacklisted websites and keeps images with
    similarity > 0.4 in the final selection folder.
    """
    # Create output folders if they don't exist
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    if not os.path.exists(final_folder):
        os.makedirs(final_folder)
    
    # Define JSON files for storing image URLs
    all_images_json = os.path.join(output_folder, "image_urls.json")
    final_images_json = os.path.join(final_folder, "final_image_urls.json")
    
    # Data dictionaries for JSON storage
    all_image_data = {}
    final_image_data = {}
    
    # Dictionary to track all URLs and their status (including blacklisted ones)
    url_tracking = {
        "total_attempts": 0,
        "successful_downloads": 0,
        "blacklisted_urls": [],
        "failed_downloads": []
    }
    
    # Extract features from the source image
    final_selection_details = []  # To store details of images copied to final_folder
    similarities = []  # Initialize to empty list

    log_message(f"✨ Extracting features from source image: {image_path}")
    source_features = extract_feature_vector(image_path)
    if source_features is None:
        log_message("❌ Error: Could not extract features from source image.")
        return []
        
    # Use the driver from Chrome_Driver.py
    driver = get_driver()
    downloaded_images = []
    downloaded_urls = {}  # Track URL for each downloaded image
    
    try:
        log_message(f"✨ Starting reverse image search for: {image_path}")
        
        # METHOD 1: Direct URL approach - more reliable
        # Encode the image file path for use in URL
        absolute_path = os.path.abspath(image_path)
        
        # Start with Google Images landing page
        driver.get("https://images.google.com")
        time.sleep(2)
        
        # Accept cookies if prompted
        try:
            accept_button = WebDriverWait(driver, 3).until(
                EC.element_to_be_clickable((By.XPATH, '//button[contains(., "Accept all") or contains(., "I agree") or contains(., "Alle akzeptieren")]'))
            )
            accept_button.click()
            log_message("  ✅ Accepted cookies.")
            time.sleep(1)
        except:
            log_message("  ℹ️ No cookie prompt appeared or already accepted.")
            
        # Click the camera icon
        try:
            camera_buttons = driver.find_elements(By.CSS_SELECTOR, 'div[aria-label="Search by image"], span[aria-label="Search by image"], svg[aria-label="Search by image"], div[aria-label="Suche anhand von Bildern"], span[aria-label="Suche anhand von Bildern"], svg[aria-label="Suche anhand von Bildern"]')
            if camera_buttons:
                camera_buttons[0].click()
                log_message("  ✅ Clicked camera icon.")
            else:
                # Alternative approach
                camera_btn = driver.find_element(By.XPATH, '//div[contains(@aria-label, "image") or contains(@title, "image")]')
                camera_btn.click()
                log_message("  ✅ Clicked camera icon (alternative).")
            time.sleep(2)
        except Exception as e:
            log_message(f"  ❌ Failed to click camera icon: {e}")
            log_message("  ℹ️ Trying direct URL access instead...")
        
        # Whether we clicked the camera or not, we'll now try the upload
        try:
            # Try to click "Upload an image" tab
            tabs = driver.find_elements(By.XPATH, '//a[contains(text(), "Upload") or contains(@aria-label, "upload") or contains(text(), "lade eine Datei hoch") or contains(@aria-label, "lade eine Datei hoch")]')
            if len(tabs) == 0:
                tabs = driver.find_elements(By.XPATH, '//span[contains(text(), "lade eine Datei hoch")]')
            if tabs:
                tabs[0].click()
                log_message("  ✅ Clicked 'Upload an image' tab.")
                time.sleep(1)
        except:
            log_message("  ℹ️ Couldn't find or click upload tab.")
        
        # Now try to find the file input
        try:
            # Use different strategies to find the file input
            try:
                file_input = WebDriverWait(driver, 5).until(
                    EC.presence_of_element_located((By.XPATH, '//input[@type="file"]'))
                )
            except:
                # Try finding any input element
                file_input = driver.find_element(By.TAG_NAME, 'input')
            
            file_input.send_keys(absolute_path)
            log_message(f"  ✅ Uploaded image: {absolute_path}")
            time.sleep(5)  # Wait for upload and search
        except Exception as e:
            log_message(f"  ❌ Failed to upload image via browser: {e}")
            log_message("  ℹ️ Trying direct URL method...")
            
            # ALTERNATIVE METHOD: Direct URL access
            # This can bypass issues with the upload UI
            file_url = f"https://www.google.com/searchbyimage/upload"
            driver.get(file_url)
            time.sleep(2)
            
            try:
                input_element = driver.find_element(By.NAME, "encoded_image")
                input_element.send_keys(absolute_path)
                
                submit_button = driver.find_element(By.XPATH, "//input[@type='submit']")
                submit_button.click()
                log_message("  ✅ Used direct upload URL method.")
            except Exception as e:
                log_message(f"  ❌ Direct URL upload also failed: {e}")
                
                # Final attempt: Use Google Lens URL
                try:
                    lens_url = "https://lens.google.com"
                    driver.get(lens_url)
                    time.sleep(3)
                    
                    # Find the upload button on Google Lens
                    upload_btn = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, 'div[aria-label*="Search"], button[aria-label*="Search"]'))
                    )
                    upload_btn.click()
                    time.sleep(1)
                    
                    # Now find the file input
                    file_input = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, '//input[@type="file"]'))
                    )
                    file_input.send_keys(absolute_path)
                    log_message("  ✅ Uploaded via Google Lens.")
                except Exception as e:
                    log_message(f"  ❌ All upload methods failed: {e}")
                    driver.save_screenshot("upload_failed.png")
                    return downloaded_images
        
        # Wait longer for the results to load
        log_message("  ⏳ Waiting for search results...")
        time.sleep(10)  # Wait longer to ensure page loads
        
        # Try to find images
        try:
            # Wait for image results to appear - try multiple CSS selectors
            try:
                WebDriverWait(driver, 20).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, 'img.rg_i, a.VFACy, div.isv-r img, a.wXeWr, .rg_ic img, img.Q4LuWd, img[id^="dimg_"]'))
                )
            except:
                if "Unable to process this search" in driver.page_source and "content guidelines" in driver.page_source:
                    log_message("  ❌ Google blocked the search due to content guidelines. Skipping...")
                    return downloaded_images
                else: 
                    log_message('  ❌ Failed to find image on google results with CSS selectors: img.rg_i, a.VFACy, div.isv-r img, a.wXeWr, .rg_ic img, img.Q4LuWd, img["id^=dimg_"]')
                    driver.save_screenshot("google_results_error1.png")
            
            # Scroll down to load more images
            driver.execute_script("window.scrollBy(0, 1000);")
            time.sleep(2)
            
            # Try to click on "Visually similar images" or "Find other sizes" if visible
            try:
                similar_link = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.XPATH, '//a[contains(., "Visual matches") or contains(., "similar") or contains(., "Find") or contains(., "sizes")]'))
                )
                similar_link.click()
                log_message("  ✅ Clicked on 'Visually similar images'")
                time.sleep(3)
            except:
                log_message("  ℹ️ Could not find 'Visually similar images' link")
            
            # Try different selectors to find images
            image_elements = []
            for selector in ['img.rg_i', 'div.isv-r img', '.VFACy img', '.wXeWr img', '.rg_ic img', 'img.Q4LuWd, img[id^="dimg_"]']:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    image_elements.extend(elements)
                    log_message(f"  🔍 Found {len(elements)} images with selector: {selector}")
            
            # If no specific selectors worked, try all images
            if not image_elements:
                image_elements = driver.find_elements(By.TAG_NAME, 'img')
                log_message(f"  🔍 Using generic approach, found {len(image_elements)} images")
            
            # Filter out tiny images and icons
            valid_images = []
            for img in image_elements:
                try:
                    width = int(img.get_attribute('width') or 0)
                    height = int(img.get_attribute('height') or 0)
                    if width > 100 and height > 100:
                        valid_images.append(img)
                except:
                    # If we can't get dimensions, include it anyway
                    valid_images.append(img)
            
            log_message(f"  ✅ Found {len(valid_images)} potentially valid images")
            
            # Process and download images
            downloaded_count = 0
            img_urls = []
            for i, img_element in enumerate(valid_images[:max_images]):
                
                # Try different attributes to get image URL
                img_url = None
                for attr in ['src', 'data-src', 'data-iurl', 'data-ils']:
                    url = img_element.get_attribute(attr)
                    if url and not url.startswith('data:') and 'gstatic' not in url and 'google' not in url:
                        img_url = url
                        break
                
                # If no direct URL found, try clicking the image
                if not img_url or 'gstatic.com' in img_url or img_url.startswith('data:'):
                    clicked_successfully = False
                    try:
                        # Scroll to the element and click
                        driver.execute_script("arguments[0].scrollIntoView(true);", img_element)
                        time.sleep(0.5)

                        # Wait for the element to be clickable and then click
                        clickable_element = WebDriverWait(driver, 5).until(EC.element_to_be_clickable(img_element))
                        clickable_element.click()
                        clicked_successfully = True
                        
                    except ElementClickInterceptedException:
                        log_message(f"  ⚠️ Click intercepted for image. Trying JavaScript click.")
                        driver.save_screenshot(f"google_click_intercepted_{i}.png")
                        try:
                            driver.execute_script("arguments[0].click();", img_element)
                            clicked_successfully = True
                        except Exception as js_click_error:
                            log_message(f"  ❌ JavaScript click also failed: {js_click_error}")
                    except TimeoutException:
                        log_message(f"  ❌ Timeout waiting for element to be clickable: {img_element.get_attribute('outerHTML')[:100]}")
                    except Exception as click_error:
                        log_message(f"  ❌ Error clicking image: {click_error}")

                    if clicked_successfully:
                        time.sleep(2) # Wait for action to complete (e.g., panel to open)
                        
                        # We need the large picture in the right panel where the picture is between div1 (contains Close button),  div2 (contains THE IMAGE, inside a link), div3 (contains Visit button)  
                        # There multiple right panels (ready for other images to bring them fast). The correct one is the last one. We have 2 ways to get it
                        try:
                            right_panel_xpath = """
                                //div[
                                    count(./div) >= 3 and
                                    ./div[1][.//button[@aria-label='Close']] and
                                    (
                                        ./div[3][.//div[@aria-label='Visit'][./span[normalize-space()='Visit']]] or
                                        ./div[3][.//a[.//span[normalize-space()='Visit']]]
                                    )
                                ]
                                """
                            potential_right_panels = driver.find_elements(By.XPATH, right_panel_xpath)
                            for panel in potential_right_panels: # The last on 
                                image_xpath_relative = ".//div[2]//img[1]"
                                large_img = panel.find_element(By.XPATH, image_xpath_relative)
                                img_urls.append(large_img.get_attribute('src'))
                            
                        except Exception as e:
                            log_message(f"  ❌ Could not find the large image using the robust XPath: {e}")
                        
                        # Back to results
                        driver.execute_script("window.history.go(-1)")
                        time.sleep(1)
                    
            try: 
                img_urls = list(set(img_urls))
                # Skip small thumbnails or base64 images
                img_urls = [url for url in img_urls if url and not url.startswith('data:') and '&w=32&' not in url and 'gstatic.com/favicon' not in url]
                url_tracking["total_attempts"] = len(img_urls)
                url_tracking["blacklisted_urls"] = [url for url in img_urls if is_blacklisted_url(url)]
                img_urls = [url for url in img_urls if not is_blacklisted_url(url)]
                
                for img_url in img_urls:
                    # log_message(f"  ⬇️ Trying to download image: {img_url[:50]}...")
                    
                    # Download the image
                    downloaded_path, url = download_google_image(img_url, output_folder)
                    if downloaded_path:
                        downloaded_count += 1
                        downloaded_images.append(downloaded_path)
                        downloaded_urls[downloaded_path] = url  # Track URL for each image
                        url_tracking["successful_downloads"] += 1
                        
                        # Store the image data for JSON
                        all_image_data[os.path.basename(downloaded_path)] = url
                    else:
                        url_tracking["failed_downloads"].append(img_url)
            
            except Exception as e:
                log_message(f"  ❌ Error processing image {i}: {e}")
            
            # If we didn't get enough images, try one last approach
            if downloaded_count < max_images:
                log_message("  ℹ️ Trying alternative method to find more images...")
                try:
                    # Click on "More sizes" or "All sizes" if available
                    size_links = driver.find_elements(By.XPATH, '//a[contains(text(), "size") or contains(text(), "View")]')
                    if size_links:
                        size_links[0].click()
                        time.sleep(3)
                        
                        # Find images in this new view
                        alt_images = driver.find_elements(By.TAG_NAME, 'img')
                        log_message(f"  🔍 Found {len(alt_images)} images in alternative view")
                        
                        for i, img in enumerate(alt_images):
                            if downloaded_count >= max_images:
                                break
                                
                            try:
                                img_url = img.get_attribute('src')
                                if img_url and not img_url.startswith('data:') and 'google' not in img_url:
                                    url_tracking["total_attempts"] += 1
                                    
                                    # Check if URL is from blacklisted website
                                    if is_blacklisted_url(img_url):
                                        url_tracking["blacklisted_urls"].append(img_url)
                                        log_message(f"  ⚠️ Skipping blacklisted image URL: {img_url[:50]}...")
                                        continue
                                        
                                    downloaded_path, url = download_google_image(img_url, output_folder)
                                    if downloaded_path:
                                        downloaded_count += 1
                                        downloaded_images.append(downloaded_path)
                                        downloaded_urls[downloaded_path] = url  # Track URL for each image
                                        url_tracking["successful_downloads"] += 1
                                        
                                        # Store the image data for JSON
                                        all_image_data[os.path.basename(downloaded_path)] = url
                                    else:
                                        url_tracking["failed_downloads"].append(img_url)
                            except Exception as e:
                                log_message(f"  ❌ Error with alternative image {i}: {e}")
                except Exception as e:
                    log_message(f"  ❌ Alternative approach failed: {e}")
        
        except TimeoutException:
            log_message("  ❌ Timed out waiting for search results to load.")
            # driver.save_screenshot("search_results_timeout.png")
            log_message("  ℹ️ Trying direct API approach as last resort...")
            
            # Final approach: Try direct search results parsing
            try:
                current_url = driver.current_url
                log_message(f"  🌐 Current URL: {current_url}")
                
                # Try to scrape image URLs directly from page source
                page_source = driver.page_source
                
                # Look for image URLs in the page source
                import re
                img_patterns = [
                    r'https?://[^"\']+\.(?:jpg|jpeg|png|webp)[^"\']*',
                    r'https?://[^"\']+\.(?:jpg|jpeg|png|webp)[^"\']*'
                ]
                
                all_urls = []
                for pattern in img_patterns:
                    matches = re.findall(pattern, page_source)
                    all_urls.extend(matches)
                
                # Filter URLs to exclude Google's own images
                filtered_urls = [url for url in all_urls if 'google' not in url and 'gstatic' not in url]
                
                log_message(f"  🔍 Found {len(filtered_urls)} potential image URLs in page source")
                
                # Download up to max_images
                for i, url in enumerate(filtered_urls):
                    if i >= max_images or len(downloaded_images) >= max_images:
                        break
                    
                    url_tracking["total_attempts"] += 1
                    
                    # Check if URL is from blacklisted website
                    if is_blacklisted_url(url):
                        url_tracking["blacklisted_urls"].append(url)
                        log_message(f"  ⚠️ Skipping blacklisted image URL: {url[:50]}...")
                        continue
                    
                    downloaded_path, img_url = download_google_image(url, output_folder)
                    if downloaded_path:
                        downloaded_images.append(downloaded_path)
                        downloaded_urls[downloaded_path] = img_url
                        
                        # Store the image data for JSON
                        all_image_data[os.path.basename(downloaded_path)] = img_url
                
            except Exception as e:
                log_message(f"  ❌ Failed to scrape images from page source: {e}")
        
        log_message(f"  ✅ Downloaded {len(downloaded_images)} images out of requested {max_images}")
        
        # Save the URLs of all downloaded images to JSON
        save_image_data_to_json(all_image_data, all_images_json)
        
        # Now compare similarity and move the most similar images to final_folder
        if downloaded_images:
            log_message("\n📊 Analyzing image similarity using MobileNetV2...")
            similarities = []  # Re-initialize for this block
            valid_images = []
            
            # First verify which downloaded images are valid
            for img_path in downloaded_images:
                if os.path.exists(img_path) and os.path.getsize(img_path) > 0:
                    try:
                        with Image.open(img_path) as test_img:
                            # Just open to check if it's valid
                            valid_images.append(img_path)
                    except Exception as e:
                        log_message(f"  ⚠️ Skipping invalid image {os.path.basename(img_path)}: {e}")
            
            if not valid_images:
                log_message("  ℹ️ No valid images downloaded to process")
                return downloaded_images
                
            log_message(f"  ✅ Found {len(valid_images)} valid images out of {len(downloaded_images)} downloaded")
            
            # Extract features and compute similarity for each valid downloaded image
            for img_path in valid_images:
                features = extract_feature_vector(img_path)
                if features is not None:
                    similarity = compute_similarity(source_features, features)
                    similarities.append((img_path, similarity))
                    # log_message(f"    🖼️ Image: {os.path.basename(img_path)}, Similarity: {similarity:.4f}")
            
            if not similarities:
                log_message("  ℹ️ Could not compute similarity for any images")
                return downloaded_images
            
            # Sort by similarity (highest first)
            similarities.sort(key=lambda x: x[1], reverse=True)
            
            # Copy the most similar images to the final folder
            copied_count = 0
            for img_path, similarity in similarities:
                if similarity >= SIMILARITY_THRESHOLD:
                    try:
                        img_filename = os.path.basename(img_path)
                        dest_path = os.path.join(final_folder, img_filename)
                        
                        # Make sure source file is valid before copying
                        if os.path.exists(img_path) and os.path.getsize(img_path) > 0:
                            # Copy with error handling
                            try:
                                shutil.copy2(img_path, dest_path)
                                copied_count += 1
                                log_message(f"  ✅ Copied to final selection: {img_filename} (similarity: {similarity:.4f})")
                                
                                # Add to final images JSON data
                                if img_filename in all_image_data:
                                    final_image_data[img_filename] = {
                                        "url": all_image_data[img_filename],
                                        "similarity": float(similarity)
                                    }
                            except Exception as e:
                                log_message(f"  ❌ Error copying {img_path} to final folder: {e}")
                    except Exception as e:
                        log_message(f"  ❌ Error processing similarity match: {e}")
            
            # Always include at least one image in final selection (the most similar) if we found any
            if copied_count == 0 and similarities:
                try:
                    best_img, best_sim = similarities[0]
                    if os.path.exists(best_img) and os.path.getsize(best_img) > 0:
                        img_filename = os.path.basename(best_img)
                        dest_path = os.path.join(final_folder, img_filename)
                        shutil.copy2(best_img, dest_path)
                        log_message(f"  ✅ Copied best match to final selection: {img_filename} (similarity: {best_sim:.4f})")
                        copied_count = 1
                        
                        # Add to final images JSON data
                        if img_filename in all_image_data:
                            final_image_data[img_filename] = {
                                "url": all_image_data[img_filename],
                                "similarity": float(best_sim)
                            }
                except Exception as e:
                    log_message(f"  ❌ Error copying best match: {e}")
            
            # Save the URLs of final selected images to JSON
            if final_image_data:
                save_image_data_to_json(final_image_data, final_images_json)
                # Populate final_selection_details with info from final_image_data
                for img_filename, data in final_image_data.items():
                    final_selection_details.append({
                        "path": os.path.join(final_folder, img_filename), # Full path to the image in the final_folder
                        "similarity": data.get("similarity", 0.0),
                        "url": data.get("url", "")
                    })
            
            log_message(f"🎉 Process complete. {copied_count} images selected in final folder.")
            
            # Print URL tracking information
            log_message(f"📋 URL Tracking Summary: Total image attempts: {url_tracking['total_attempts']}, Successful downloads: {url_tracking['successful_downloads']}, Blacklisted URLs skipped: {len(url_tracking['blacklisted_urls'])}, Failed downloads: {len(url_tracking['failed_downloads'])}\n")
            
        
    except Exception as e:
        log_message(f"❌ Error during reverse image search: {e}")
        driver.save_screenshot("google_results_error2.png")
        # driver.save_screenshot("error_screenshot.png")
        
    finally:
        driver.quit()
        
    langfuse.get_client().update_current_span(
        output={
            "DownloadedImagesURL": downloaded_urls,
            "DownloadedImagesWithSimilarity": similarities,
            "FinalSelectionDetails": final_selection_details
        }
    )
        
    return final_selection_details




# for running script seperately
def process_images_in_folder(input_folder, max_images=10, threshold=0.8):
    global SIMILARITY_THRESHOLD
    SIMILARITY_THRESHOLD = threshold

    # Define shared output and final folders inside input folder
    output_folder = os.path.join(input_folder, "similar_images")
    final_folder = os.path.join(input_folder, "final_selection")
    os.makedirs(output_folder, exist_ok=True)
    os.makedirs(final_folder, exist_ok=True)

    # Define JSON files for all images in the run
    all_run_json = os.path.join(input_folder, "all_image_urls.json")
    all_run_data = {}

    # Collect image files
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.webp', '*.bmp', '*.tiff']
    image_files = []
    for ext in image_extensions:
        image_files.extend(glob.glob(os.path.join(input_folder, ext)))
        image_files.extend(glob.glob(os.path.join(input_folder, ext.upper())))

    if not image_files:
        log_message(f"  ℹ️ No images found in {input_folder}")
        return

    log_message(f"  🔍 Found {len(image_files)} images. Processing...")

    # Process each image
    for i, image_path in enumerate(image_files):
        log_message(f"  [{i+1}/{len(image_files)}] Processing: {os.path.basename(image_path)}")
        
        # Create a sub-entry for this source image
        source_name = os.path.basename(image_path)
        all_run_data[source_name] = {"similar_images": {}}
        
        # Run reverse search
        downloaded_imgs = reverse_search_on_google(image_path, output_folder, final_folder, max_images)
        
        # Collect all data for this source image
        if os.path.exists(os.path.join(output_folder, "image_urls.json")):
            with open(os.path.join(output_folder, "image_urls.json"), 'r') as f:
                img_data = json.load(f)
                all_run_data[source_name]["similar_images"] = img_data
        
        if os.path.exists(os.path.join(final_folder, "final_image_urls.json")):
            with open(os.path.join(final_folder, "final_image_urls.json"), 'r') as f:
                final_data = json.load(f)
                all_run_data[source_name]["final_selection"] = final_data
        
        # Save the combined data after each image
        with open(all_run_json, 'w') as f:
            json.dump(all_run_data, f, indent=4)
        
        if i < len(image_files) - 1:
            log_message("  ⏳ Waiting to avoid rate limiting...")
            time.sleep(5)

    log_message(f"🎉 Processing complete. All image URLs saved to {all_run_json}")


def download_google_image(url, save_dir, filename=None):
    """Download an image from URL and save it to disk"""
    try:
        # Check if the URL is from a blacklisted domain
        if is_blacklisted_url(url):
            log_message(f"  ⚠️ Skipping blacklisted URL: {url}")
            return None, url  # Return the URL to log it
        
        # Set headers to mimic a browser request
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Referer': 'https://www.google.com/'
        }
        
        response = requests.get(url, headers=headers, timeout=15)
        response.raise_for_status()
        
        # Check if response content is an image
        content_type = response.headers.get('Content-Type', '')
        if not content_type.startswith('image/'):
            log_message(f"  ⚠️ Warning: URL did not return an image. Content-Type: {content_type}")
        
        # Verify content length
        if len(response.content) < 1000:  # Extremely small files are likely not valid images
            log_message(f"  ⚠️ Warning: Downloaded content is too small ({len(response.content)} bytes)")
            return None, url
        
        # Try to open the image to verify it's valid
        try:
            img = Image.open(BytesIO(response.content))
            # Verify it's an image
            img.verify()
            format_name = img.format
            
            # Generate a filename if none provided, use correct extension
            if not filename:
                if format_name == 'JPEG':
                    ext = '.jpg'
                elif format_name:
                    ext = f'.{format_name.lower()}'
                else:
                    ext = '.jpg'  # Default
                filename = f"google_image_{uuid.uuid4().hex[:8]}{ext}"
            
            file_path = os.path.join(save_dir, f"google_{filename}")
            
            # Save the image using PIL for better format control
            img = Image.open(BytesIO(response.content))
            if img.mode == 'RGBA' and filename.lower().endswith(('.jpg', '.jpeg')):
                img = img.convert('RGB')  # Convert RGBA to RGB for JPEG
                
            img.save(file_path)
            
            log_message(f"  ⬇️  Successfully downloaded: {file_path}")
            
            # Remove white background only if image is saved successfully
            if os.path.exists(file_path) and os.path.getsize(file_path) > 0:
                remove_white_background(file_path)
            
            return file_path, url
            
        except Exception as e:
            log_message(f"  ❌ Downloaded file is not a valid image: {e}")
            # Try alternative direct saving method
            try:
                if not filename:
                    filename = f"google_image_{uuid.uuid4().hex[:8]}.jpg"
                
                file_path = os.path.join(save_dir, filename)
                
                # Direct save without verification
                with open(file_path, 'wb') as f:
                    f.write(response.content)
                
                # Check if file is empty
                if os.path.getsize(file_path) == 0:
                    log_message("  ❌ Downloaded file is empty, removing it")
                    os.remove(file_path)
                    return None, url
                
                log_message(f"  ✅ Directly saved image (unverified): {file_path}")
                return file_path, url
                
            except Exception as e:
                log_message(f"  ❌ Failed to save image directly: {e}")
                return None, url
            
    except Exception as e:
        log_message(f"  ❌ Failed to download image: {e}")
        return None, url



if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Batch Google Reverse Image Search with MobileNetV2 Similarity Filtering")
    parser.add_argument("input_path", help="Path to image file or folder containing images")
    parser.add_argument("--max", type=int, default=5, help="Maximum number of images to download per input image")
    parser.add_argument("--threshold", type=float, default=0.8, help="Similarity threshold (0.0-1.0)")
    args = parser.parse_args()

    if os.path.isfile(args.input_path):
        # Single image case
        input_folder = os.path.dirname(args.input_path)
        output_folder = os.path.join(input_folder, "similar_images")
        final_folder = os.path.join(input_folder, "final_selection")
        os.makedirs(output_folder, exist_ok=True)
        os.makedirs(final_folder, exist_ok=True)

        SIMILARITY_THRESHOLD = args.threshold
        reverse_search_on_google(args.input_path, output_folder, final_folder, args.max)
    elif os.path.isdir(args.input_path):
        # Folder case
        process_images_in_folder(args.input_path, max_images=args.max, threshold=args.threshold)
    else:
        log_message(f"❌ Invalid path: {args.input_path}")