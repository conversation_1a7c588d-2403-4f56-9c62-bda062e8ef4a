# IP/Patents/patent_db.py
"""
Patent Database Operations Module

This module provides functions to interact with the PostgreSQL database
for patent grant and CPC data.
"""

import os
import psycopg2
import psycopg2.extras
import logging
from dotenv import load_dotenv
import json # For handling complex fields like lists of inventors/applicants if needed

# Load environment variables
load_dotenv()

# Configure logging
log_file_path = os.path.join(os.path.dirname(__file__), '..', '..', 'logs', 'patent_db.log')
os.makedirs(os.path.dirname(log_file_path), exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file_path),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def get_db_connection():
    """
    Create a connection to the PostgreSQL database.

    Returns:
        connection: PostgreSQL database connection object or None if connection fails.
    """
    conn = None
    try:
        conn = psycopg2.connect(
            host=os.getenv("POSTGRES_HOST"),
            port=os.getenv("POSTGRES_PORT"),
            user=os.getenv("POSTGRES_USER"),
            password=os.getenv("POSTGRES_PASSWORD"),
            dbname=os.getenv("POSTGRES_DB")
        )
        logger.info("Database connection established successfully.")
        return conn
    except psycopg2.OperationalError as e:
        logger.error(f"Database connection failed: {e}")
        # Depending on the application's needs, you might want to retry or handle this differently.
        # For now, we'll return None to indicate failure.
        return None
    except Exception as e:
        logger.error(f"An unexpected error occurred during database connection: {e}")
        # Ensure connection is closed if partially opened before the error
        if conn:
            conn.close()
        return None


def upsert_patent_grants(patent_grants):
    """
    Insert or update patent grant records in the database using UPSERT logic.
    Filters out records without a 'publication_doc_number'.

    Args:
        patent_grants (list): List of patent grant dictionaries (record_type='grant').

    Returns:
        int: Number of records successfully processed.
    """
    if not patent_grants:
        logger.info("No patent grants provided for upsert.")
        return 0

    # Filter out grants without the primary identifier and ensure correct record_type
    valid_grants = [
        grant for grant in patent_grants
        if grant.get('record_type') == 'grant' and grant.get('publication_doc_number')
    ]
    original_count = len(patent_grants)
    valid_count = len(valid_grants)
    skipped_count = original_count - valid_count
    logger.info(f"Received {original_count} records. Processing {valid_count} valid grant records (skipped {skipped_count}).")

    if not valid_grants:
        logger.info("No valid patent grant records left to upsert after filtering.")
        return 0

    conn = None
    processed_count = 0
    cursor = None # Initialize cursor to None
    try:
        conn = get_db_connection()
        if conn is None:
            logger.error("Failed to get database connection. Aborting upsert.")
            return 0 # Indicate failure or inability to process

        cursor = conn.cursor()

        # Define the columns for INSERT/UPDATE (excluding id, CPC fields, create_time, update_time)
        # Map parser fields to DB columns
        columns = [
            'document_id', 'reg_no', 'TRO', 'inventors', 'assignee', 'applicant',
            'patent_title', 'date_published', 'plaintiff_ids', 'patent_type',
            'abstract', 'associated_patents', 'design_page_numbers', 'pdf_source',
            'image_source', 'certificate_source'
        ]
        column_names = ', '.join(columns)
        placeholders = ', '.join(['%s'] * len(columns))

        # Build the SET clause for the UPDATE part (exclude the conflict target 'document_id')
        update_set_parts = []
        for col in columns:
            if col != 'document_id':
                update_set_parts.append(f"{col} = EXCLUDED.{col}")
        update_set = ', '.join(update_set_parts)

        sql = f"""
        INSERT INTO patents ({column_names})
        VALUES ({placeholders})
        ON CONFLICT (document_id)
        DO UPDATE SET {update_set}, update_time = NOW() -- Also update the timestamp
        """

        batch_values = []
        for grant in valid_grants:
            # Prepare data - handle potential missing keys and data types
            inventors_list = grant.get('inventors', [])
            inventors_str = ", ".join([f"{inv.get('first_name', '')} {inv.get('last_name', '')}".strip() for inv in inventors_list if f"{inv.get('first_name', '')} {inv.get('last_name', '')}".strip()])

            applicants_list = grant.get('applicants', [])
            # Combine orgname and names for applicant string
            applicant_parts = []
            for app in applicants_list:
                name = f"{app.get('first_name', '')} {app.get('last_name', '')}".strip()
                org = app.get('orgname', '')
                part = f"{name} ({org})" if name and org else name or org
                if part:
                    applicant_parts.append(part)
            applicant_str = ", ".join(applicant_parts)


            # Map grant data to the order of 'columns'
            row_values = (
                grant.get('publication_doc_number'), # document_id
                grant.get('publication_doc_number'), # reg_no (using doc_number as fallback)
                grant.get('TRO'), # TRO (likely None)
                inventors_str or None, # inventors
                None, # assignee (Not directly available in parser output)
                applicant_str or None, # applicant
                grant.get('invention_title'), # patent_title
                grant.get('publication_date'), # date_published
                grant.get('plaintiff_ids', []), # plaintiff_ids (default to empty list)
                grant.get('publication_kind'), # patent_type (using kind code)
                grant.get('description'), # abstract (using description field from parser)
                grant.get('associated_patents', []), # associated_patents (default to empty list)
                grant.get('design_page_numbers', []), # design_page_numbers (default to empty list)
                grant.get('pdf_source'), # pdf_source (likely None)
                grant.get('image_source'), # image_source (likely None)
                grant.get('certificate_source') # certificate_source (likely None)
            )
            batch_values.append(row_values)

        # Execute batch upsert
        if batch_values:
            psycopg2.extras.execute_batch(cursor, sql, batch_values, page_size=500) # Adjust page_size as needed
            conn.commit()
            processed_count = len(batch_values)
            logger.info(f"Successfully upserted {processed_count} patent grant records.")
        else:
             logger.info("No data prepared for batch upsert.")


    except psycopg2.DatabaseError as db_err:
        logger.error(f"Database error during patent grant upsert: {db_err}")
        if conn:
            conn.rollback()
    except Exception as e:
        logger.error(f"Unexpected error during patent grant upsert: {e}", exc_info=True)
        if conn:
            conn.rollback()
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()
            logger.info("Database connection closed.")

    return processed_count


def update_patents_with_cpc(cpc_records):
    """
    Updates existing patent records with CPC classification data.
    Only updates records where a matching patent (by document_id) is found.
    Uses the first classification found in the CPC record's list.

    Args:
        cpc_records (list): List of CPC record dictionaries (record_type='cpc').

    Returns:
        int: Number of patent records successfully updated.
    """
    if not cpc_records:
        logger.info("No CPC records provided for update.")
        return 0

    # Filter for valid CPC records
    valid_cpc = [
        cpc for cpc in cpc_records
        if cpc.get('record_type') == 'cpc' and cpc.get('PatentNumber') and cpc.get('cpc_classifications')
    ]
    original_count = len(cpc_records)
    valid_count = len(valid_cpc)
    skipped_count = original_count - valid_count
    logger.info(f"Received {original_count} CPC records. Processing {valid_count} valid records with classifications (skipped {skipped_count}).")


    if not valid_cpc:
        logger.info("No valid CPC records left to process after filtering.")
        return 0

    conn = None
    updated_count = 0
    cursor = None # Initialize cursor to None
    try:
        conn = get_db_connection()
        if conn is None:
            logger.error("Failed to get database connection. Aborting CPC update.")
            return 0

        cursor = conn.cursor()

        # Prepare the UPDATE statement
        # Note: patent_number is also updated as it's part of the CPC section in the schema
        sql = """
        UPDATE patents
        SET
            patent_number = %(patent_num)s,
            grant_date = %(grant_date)s,
            kind_code = %(kind_code)s,
            application_number = %(app_num)s,
            cpc_section = %(section)s,
            cpc_class = %(class)s,
            cpc_subclass = %(subclass)s,
            cpc_main_group = %(main_group)s,
            cpc_subgroup = %(subgroup)s,
            symbol_position_code = %(symbol_pos)s,
            cpc_classification_value_code = %(value_code)s,
            classification_version_date = %(version_date)s,
            update_time = NOW()
        WHERE document_id = %(doc_id)s
        """

        processed_records = 0
        # Consider batching later if performance is critical
        for cpc in valid_cpc:
            # Use the first classification available
            # Ensure there is at least one classification
            if not cpc['cpc_classifications']:
                logger.warning(f"Skipping CPC record for PatentNumber {cpc.get('PatentNumber')} due to empty 'cpc_classifications' list.")
                continue

            classification = cpc['cpc_classifications'][0]
            patent_doc_id = cpc.get('PatentNumber')

            params = {
                'patent_num': patent_doc_id,
                'grant_date': cpc.get('PatentGrantDate'),
                'kind_code': cpc.get('PatentKindCode'),
                'app_num': cpc.get('ApplicationNumber'),
                'section': classification.get('section'),
                'class': classification.get('class'),
                'subclass': classification.get('subclass'),
                'main_group': classification.get('main_group'),
                'subgroup': classification.get('subgroup'),
                'symbol_pos': classification.get('symbol'), # Mapped from 'symbol' in parser
                'value_code': classification.get('value'), # Mapped from 'value' in parser
                'version_date': classification.get('version_date'),
                'doc_id': patent_doc_id
            }

            try:
                cursor.execute(sql, params)
                # Check if the update affected any row
                if cursor.rowcount > 0:
                    updated_count += 1
                # Commit after each successful update (or batch later)
                conn.commit()
                processed_records += 1
                if processed_records % 100 == 0: # Log progress periodically
                     logger.info(f"Processed {processed_records}/{valid_count} CPC updates...")

            except Exception as update_err:
                logger.error(f"Error updating patent {patent_doc_id} with CPC data: {update_err}")
                conn.rollback() # Rollback the single failed update

        logger.info(f"Finished CPC update process. Successfully updated {updated_count} patent records.")

    except psycopg2.DatabaseError as db_err:
        logger.error(f"Database error during CPC update: {db_err}")
        if conn:
            conn.rollback() # Rollback any pending transaction
    except Exception as e:
        logger.error(f"Unexpected error during CPC update: {e}", exc_info=True)
        if conn:
            conn.rollback()
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()
            logger.info("Database connection closed.")

    return updated_count

# Example usage block (optional, for testing)
if __name__ == '__main__':
    logger.info("Running patent_db.py as main script (Example Usage)")

    # Example Grant Data (mimicking parser output)
    sample_grants = [
        {
            'record_type': 'grant',
            'publication_doc_number': '11234567',
            'publication_date': '2023-01-01',
            'publication_kind': 'B2',
            'invention_title': 'Example Patent Title 1',
            'inventors': [{'first_name': 'John', 'last_name': 'Doe'}],
            'applicants': [{'first_name': 'Jane', 'last_name': 'Smith', 'orgname': 'Acme Corp'}],
            'description': 'This is the abstract/description.',
            # other fields would be None or empty lists based on mapping
        },
        {
            'record_type': 'grant',
            'publication_doc_number': '11999888',
            'publication_date': '2023-02-15',
            'publication_kind': 'B1',
            'invention_title': 'Another Patent Title',
            'inventors': [{'first_name': 'Peter', 'last_name': 'Jones'}],
            'applicants': [], # No applicants
            'description': 'Description for the second patent.',
        },
         {
            'record_type': 'grant', # Missing doc number - should be skipped
            'publication_date': '2023-03-20',
            'invention_title': 'Skipped Patent',
        }
    ]

    # Example CPC Data (mimicking parser output)
    sample_cpc = [
        {
            'record_type': 'cpc',
            'PatentNumber': '11234567', # Matches first grant
            'PatentGrantDate': '2023-01-01',
            'PatentKindCode': 'B2',
            'ApplicationNumber': '16000001',
            'cpc_classifications': [
                {
                    'type': 'main', 'section': 'G', 'class': '06', 'subclass': 'F',
                    'main_group': '16', 'subgroup': '2457', 'symbol': 'L',
                    'value': 'I', 'version_date': '2021-01-01'
                }
            ]
        },
        {
            'record_type': 'cpc',
            'PatentNumber': '99999999', # No matching grant - should not update anything
            'PatentGrantDate': '2022-12-31',
            'PatentKindCode': 'B2',
            'ApplicationNumber': '15999999',
            'cpc_classifications': [
                 {
                    'type': 'main', 'section': 'H', 'class': '04', 'subclass': 'L',
                    'main_group': '9', 'subgroup': '08', 'symbol': 'F',
                    'value': 'I', 'version_date': '2021-01-01'
                }
            ]
        }
    ]

    logger.info("--- Testing upsert_patent_grants ---")
    grants_processed = upsert_patent_grants(sample_grants)
    logger.info(f"Upserted {grants_processed} grant records.")

    logger.info("--- Testing update_patents_with_cpc ---")
    cpc_updated = update_patents_with_cpc(sample_cpc)
    logger.info(f"Updated {cpc_updated} records with CPC data.")

    logger.info("patent_db.py example finished.")