import os
import sys
sys.path.append(os.getcwd())
from Alerts.PicturesProcessing.RemoveDuplicates import remove_exact_duplicates, remove_near_duplicates, remove_text_duplicates

my_ip_folder = "D:\\Documents\\Programing\\TRO\\USside\\Documents\\AssessFiles\\For AI\\IP"
list_of_images = os.listdir(my_ip_folder)

print(f"Removing exact duplicates from {len(list_of_images)} images") #5293
removed_list_of_images = remove_exact_duplicates(my_ip_folder, list_of_images)
print(f"Removed {len(removed_list_of_images)} images") #2671
print("--------------------------------")
list_of_images = os.listdir(my_ip_folder)
print(f"Removing near duplicates from {len(list_of_images)} images")
removed_list_of_images = remove_near_duplicates(my_ip_folder, list_of_images, hash_size=16, threshold=10)
print(f"Removed {len(removed_list_of_images)} images")
print("--------------------------------")
list_of_images = os.listdir(my_ip_folder)
print(f"Remaining: {len(list_of_images)} images")
