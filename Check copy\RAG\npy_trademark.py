import os
import cv2
import numpy as np
from tqdm import tqdm
import zlib
import base64
import json
from DatabaseManagement.ImportExport import get_table_from_GZ
from Check.Do_Check_Download import download_from_url
import asyncio
from Common.Constants import sanitize_name
from Check.RAG.Collect_Images import remove_duplicates
from langfuse import observe

# How to use:
# 1. Run get_trademarks() to download the trademarks
# 2. Remove text from logo folder (5 min)
# 3. Identify logo in the text folder and move to logo folder (15 Minutes for 6,000 files)
# 4. Remove duplicates in logo folder : remove_duplicates('D:/Documents/Programing/TRO/USside/Documents/AssessFiles/For AI/Trademarks/Logo - NoDupe/')
# 5. Build descriptors dataset : build_descriptors_dataset()

@observe()
def find_most_similar_trademark_logo(product_image_path, plaintiff_id=None, top_n=1, similarity_threshold=0.7):
    """
    Find the most similar trademark logo using SigLIP embeddings instead of ORB descriptors.

    Args:
        product_image_path (str): Path to the product image.
        plaintiff_id (int, optional): Filter by specific plaintiff ID.
        top_n (int): Number of top matches to return.
        similarity_threshold (float): Minimum cosine similarity score to consider a match.

    Returns:
        dict or None: Match information for the top match, or None if no match is found.
    """
    from Check.RAG.RAG_Inference import get_siglip_embeddings
    from sklearn.metrics.pairwise import cosine_similarity


    from Check.RAG.RAG_Inference import structured_trademark_array
    data = structured_trademark_array

    if plaintiff_id:
        structured_trademark_array = structured_trademark_array[structured_trademark_array['plaintiff_id'] == plaintiff_id]

    if len(structured_trademark_array) == 0:
        print("No trademark data available for the specified criteria.")
        return None

    # Generate SigLIP embedding for the product image
    try:
        product_embedding = get_siglip_embeddings([product_image_path], data_type="image")[0]
    except Exception as e:
        print(f"Error generating embedding for product image: {e}")
        return None

    # Extract embeddings from the structured array
    trademark_embeddings = np.array([entry['embedding_siglip'] for entry in structured_trademark_array])

    # Compute cosine similarity between product and all trademark embeddings
    similarities = cosine_similarity([product_embedding], trademark_embeddings)[0]

    # Find matches above threshold
    potential_matches = []
    for idx, similarity_score in enumerate(similarities):
        if similarity_score >= similarity_threshold:
            trademark_entry = structured_trademark_array[idx]
            match_info = {
                'filename': trademark_entry['filename'],
                'full_filename': trademark_entry['full_filename'],
                'plaintiff_name': trademark_entry['plaintiff_name'],
                'plaintiff_id': trademark_entry['plaintiff_id'],
                'docket': trademark_entry['docket'],
                'number_of_cases': trademark_entry['number_of_cases'],
                'reg_no': trademark_entry['reg_no'],
                'int_cls_list': trademark_entry['int_cls_list'],
                'similarity': similarity_score
            }
            potential_matches.append(match_info)

    if potential_matches:
        # Sort by similarity score (descending)
        potential_matches.sort(key=lambda x: x['similarity'], reverse=True)
        return potential_matches[:top_n]  # Return top N matches
    else:
        return None

if __name__ == "__main__":
    # 1. Get trademarks
    # asyncio.run(get_trademarks())
    
    # 2. Remove duplicates
    # from Alerts.PicturesProcessing.RemoveDuplicates import remove_exact_duplicates, remove_near_duplicates, remove_text_duplicates

    my_ip_folder = "D:/Documents/Programing/TRO/USside/Documents/AssessFiles/For AI/Trademarks/Logo - NoDupe/"
    # list_of_images = os.listdir(my_ip_folder)

    for file in os.listdir(my_ip_folder):
        os.rename(os.path.join(my_ip_folder, file), os.path.join(my_ip_folder, file + ".webp"))


    # print(f"Removing exact duplicates from {len(list_of_images)} images") #5293
    # removed_list_of_images = remove_exact_duplicates(my_ip_folder, list_of_images)
    # print(f"Removed {len(removed_list_of_images)} images") #2671
    # print("--------------------------------")
    # list_of_images = os.listdir(my_ip_folder)
    # print(f"Removing near duplicates from {len(list_of_images)} images")
    # removed_list_of_images = remove_near_duplicates(my_ip_folder, list_of_images, hash_size=12, threshold=24)
    # print(f"Removed {len(removed_list_of_images)} images")
    # print("--------------------------------")
    # list_of_images = os.listdir(my_ip_folder)
    # print(f"Remaining: {len(list_of_images)} images")


    # 4. Find most similar trademark
    # find_most_similar_trademark("D:/Documents/Programing/TRO/USside/Documents/AssessFiles/For AI/ChromeHeart/445_A_Chrome Hearts.jpg")


    # find_most_similar_trademark("D:/Documents/Programing/TRO/USside/Documents/AssessFiles/For AI/ChromeHeart/444_A_Chrome Hearts_cropped.jpg")
    # find_most_similar_trademark("D:/Documents/Programing/TRO/USside/Documents/AssessFiles/For AI/ChromeHeart/443_A_Chrome Hearts_cropped.jpg")
