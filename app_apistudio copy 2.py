
from langfuse import Langfuse, observe
import langfuse


# Initialize Langfuse client
try:
    # langfuse_client = langfuse.get_client(public_key="pk-lf-bac4b191-590d-4059-89d6-e5ff73e91b48")  # Api studio project
    langfuse_client = Langfuse(public_key="pk-lf-bac4b191-590d-4059-89d6-e5ff73e91b48", secret_key="sk-lf-53f4bebe-bb34-47d6-92e7-43a6430e06d9") 
except Exception as e:
    print(f"Warning: Langfuse initialization failed: {e}")
    langfuse_client = None
