2025-04-17 17:51:38,379 - __main__ - INFO - Running test processing for 2025-03-15 to 2025-03-22...
2025-04-17 17:51:38,380 - __main__ - INFO - Processing Grant files from 2025-03-15 to 2025-03-22
2025-04-17 17:51:38,380 - __main__ - INFO - 
=== Processing Grant File for Date: 2025-03-18 ===
2025-04-17 17:51:38,380 - __main__ - INFO - --- Starting processing for I20250318.zip (grant) ---
2025-04-17 17:51:38,380 - __main__ - INFO - Attempting download from: https://api.uspto.gov/api/v1/datasets/products/files/PTGRDT/I20250318.zip
2025-04-17 17:51:38,383 - patent_file - INFO - Attempting to download file from https://api.uspto.gov/api/v1/datasets/products/files/PTGRDT/I20250318.zip to D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Zip\I20250318.zip
2025-04-17 17:51:40,010 - patent_file - INFO - Successfully downloaded file from https://api.uspto.gov/api/v1/datasets/products/files/PTGRDT/I20250318.zip to D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Zip\I20250318.zip
2025-04-17 17:51:40,011 - __main__ - INFO - Downloaded successfully to: D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Zip\I20250318.zip
2025-04-17 17:51:40,012 - __main__ - INFO - Using temporary extraction directory: D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Extracted\patent_grant_extract_lpn8jfyb
2025-04-17 17:51:40,012 - __main__ - INFO - Starting NAS transfer for ZIP: D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Zip\I20250318.zip -> /Maidalv/IP TRO Data/IP/Patents/USPTO_Grants/Zip/I20250318.zip
2025-04-17 17:51:40,012 - __main__ - INFO - Extracting I20250318.zip to D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Extracted\patent_grant_extract_lpn8jfyb...
2025-04-17 17:51:40,013 - patent_file - INFO - Attempting to send file D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Zip\I20250318.zip to NAS at /Maidalv/IP TRO Data/IP/Patents/USPTO_Grants/Zip/I20250318.zip
2025-04-17 17:51:40,014 - patent_file - INFO - Created unique extraction directory: D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Extracted\patent_grant_extract_lpn8jfyb\tmptcdnf0_s
2025-04-17 17:51:40,145 - patent_file - ERROR - Error: D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Zip\I20250318.zip is not a valid ZIP file or is corrupted.
2025-04-17 17:51:40,146 - patent_file - INFO - Attempting to clean up extraction directory: D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Extracted\patent_grant_extract_lpn8jfyb\tmptcdnf0_s
2025-04-17 17:51:40,146 - patent_file - INFO - Successfully removed extraction directory: D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Extracted\patent_grant_extract_lpn8jfyb\tmptcdnf0_s
2025-04-17 17:51:40,147 - __main__ - ERROR - Error during processing of I20250318.zip within temp dir D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Extracted\patent_grant_extract_lpn8jfyb: unsupported operand type(s) for /: 'WindowsPath' and 'list'
Traceback (most recent call last):
  File "D:\Documents\Programing\TRO\USside\IP\Patents\weekly_patent_report.py", line 194, in process_patent_zip
    extracted_xml_paths = [str(temp_extract_dir / p) for p in extracted_xml_paths_rel]
                               ~~~~~~~~~~~~~~~~~^~~
TypeError: unsupported operand type(s) for /: 'WindowsPath' and 'list'
2025-04-17 17:51:40,149 - __main__ - INFO - Attempting to await background tasks after error...
2025-04-17 17:51:40,576 - paramiko.transport - INFO - Connected (version 2.0, client OpenSSH_8.2)
2025-04-17 17:51:42,017 - paramiko.transport - INFO - Authentication (publickey) successful!
2025-04-17 17:51:42,017 - patent_file - ERROR - Error sending file D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Zip\I20250318.zip to NAS at /Maidalv/IP TRO Data/IP/Patents/USPTO_Grants/Zip/I20250318.zip: 'NASConnection' object has no attribute 'execute_remote_command'
Traceback (most recent call last):
  File "D:\Documents\Programing\TRO\USside\IP\Patents\patent_file.py", line 198, in send_to_nas
    nas.execute_remote_command(f'mkdir -p "{remote_dir}"') # Basic mkdir
    ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NASConnection' object has no attribute 'execute_remote_command'
2025-04-17 17:51:42,021 - __main__ - INFO - --- Finished processing I20250318.zip in 3.64 seconds. Records: 0, Images: 0 ---
2025-04-17 17:51:42,021 - __main__ - INFO - 
=== Grant Processing Summary (2025-03-15 to 2025-03-22) ===
2025-04-17 17:51:42,021 - __main__ - INFO - Processed 0 grant files.
2025-04-17 17:51:42,022 - __main__ - INFO - Total records processed: 0
2025-04-17 17:51:42,022 - __main__ - INFO - Total images processed: 0
2025-04-17 17:51:42,022 - __main__ - INFO - ==========================================================
2025-04-17 17:51:42,022 - __main__ - INFO - Test processing completed. Records processed: (0, 0)
2025-04-17 17:53:30,930 - __main__ - INFO - Running test processing for 2025-03-15 to 2025-03-22...
2025-04-17 17:53:30,931 - __main__ - INFO - Processing Grant files from 2025-03-15 to 2025-03-22
2025-04-17 17:53:30,931 - __main__ - INFO - 
=== Processing Grant File for Date: 2025-03-18 ===
2025-04-17 17:53:30,931 - __main__ - INFO - --- Starting processing for I20250318.zip (grant) ---
2025-04-17 17:53:30,932 - __main__ - INFO - Attempting download from: https://api.uspto.gov/api/v1/datasets/products/files/PTGRDT/I20250318.zip
2025-04-17 17:53:30,934 - patent_file - INFO - Attempting to download file from https://api.uspto.gov/api/v1/datasets/products/files/PTGRDT/I20250318.zip to D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Zip\I20250318.zip
2025-04-17 17:53:32,582 - patent_file - INFO - Successfully downloaded file from https://api.uspto.gov/api/v1/datasets/products/files/PTGRDT/I20250318.zip to D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Zip\I20250318.zip
2025-04-17 17:53:32,583 - __main__ - INFO - Downloaded successfully to: D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Zip\I20250318.zip
2025-04-17 17:53:32,584 - __main__ - INFO - Using temporary extraction directory: D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Extracted\patent_grant_extract_lnzymrjm
2025-04-17 17:53:32,585 - __main__ - INFO - Starting NAS transfer for ZIP: D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Zip\I20250318.zip -> /Maidalv/IP TRO Data/IP/Patents/USPTO_Grants/Zip/I20250318.zip
2025-04-17 17:53:32,585 - __main__ - INFO - Extracting I20250318.zip to D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Extracted\patent_grant_extract_lnzymrjm...
2025-04-17 17:53:32,586 - patent_file - INFO - Attempting to send file D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Zip\I20250318.zip to NAS at /Maidalv/IP TRO Data/IP/Patents/USPTO_Grants/Zip/I20250318.zip
2025-04-17 17:53:32,587 - patent_file - INFO - Created unique extraction directory: D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Extracted\patent_grant_extract_lnzymrjm\tmpiqa9z454
2025-04-17 17:53:32,684 - patent_file - ERROR - Error: D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Zip\I20250318.zip is not a valid ZIP file or is corrupted.
2025-04-17 17:53:32,685 - patent_file - INFO - Attempting to clean up extraction directory: D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Extracted\patent_grant_extract_lnzymrjm\tmpiqa9z454
2025-04-17 17:53:32,685 - patent_file - INFO - Successfully removed extraction directory: D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Extracted\patent_grant_extract_lnzymrjm\tmpiqa9z454
2025-04-17 17:53:32,686 - __main__ - WARNING - No XML files found or extraction failed for I20250318.zip. Check ZIP structure, extraction logic, or logs for errors like BadZipFile.
2025-04-17 17:53:32,686 - __main__ - INFO - Starting NAS transfer for extracted folder: D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Extracted\patent_grant_extract_lnzymrjm -> /Maidalv/IP TRO Data/IP/Patents/USPTO_Grants/Extracted/I20250318
2025-04-17 17:53:32,686 - __main__ - INFO - Waiting for background tasks (NAS Zip, DB, NAS Extract)...
2025-04-17 17:53:32,686 - patent_file - INFO - Attempting to send folder D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Extracted\patent_grant_extract_lnzymrjm to NAS at /Maidalv/IP TRO Data/IP/Patents/USPTO_Grants/Extracted/I20250318
2025-04-17 17:53:33,195 - paramiko.transport - INFO - Connected (version 2.0, client OpenSSH_8.2)
2025-04-17 17:53:33,258 - paramiko.transport - INFO - Connected (version 2.0, client OpenSSH_8.2)
2025-04-17 17:53:34,618 - paramiko.transport - INFO - Authentication (publickey) successful!
2025-04-17 17:53:34,867 - paramiko.transport - INFO - Authentication (publickey) successful!
2025-04-17 17:53:41,921 - patent_file - INFO - Successfully sent folder D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Extracted\patent_grant_extract_lnzymrjm to NAS at /Maidalv/IP TRO Data/IP/Patents/USPTO_Grants/Extracted/I20250318
2025-04-17 17:53:58,448 - patent_file - ERROR - Error sending file D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Zip\I20250318.zip to NAS at /Maidalv/IP TRO Data/IP/Patents/USPTO_Grants/Zip/I20250318.zip: SCP failed after 3 attempts. Final error: Warning: Permanently added 'jslawny.synology.me' (ED25519) to the list of known hosts.
scp: dest open "/Maidalv/IP TRO Data/IP/Patents/USPTO_Grants/Zip/I20250318.zip": No such file or directory
scp: failed to upload file D:/Documents/Programing/TRO/USside/Documents/IP/Patents/USPTO_Grants/Zip/I20250318.zip to /Maidalv/IP TRO Data/IP/Patents/USPTO_Grants/Zip/I20250318.zip
Traceback (most recent call last):
  File "D:\Documents\Programing\TRO\USside\FileManagement\NAS.py", line 379, in transfer_file_with_scp
    result = subprocess.run(
             ^^^^^^^^^^^^^^^
  File "C:\ProgramData\miniconda3\Lib\subprocess.py", line 571, in run
    raise CalledProcessError(retcode, process.args,
subprocess.CalledProcessError: Command '['scp', '-r', '-i', 'D:\\Documents\\Programing\\TRO\\USside\\.ssh\\id_rsa', '-P', '22', '-o', 'StrictHostKeyChecking=no', '-o', 'UserKnownHostsFile=/dev/null', 'D:\\Documents\\Programing\\TRO\\USside\\Documents\\IP\\Patents\\USPTO_Grants\\Zip\\I20250318.zip', '<EMAIL>:/Maidalv/IP TRO Data/IP/Patents/USPTO_Grants/Zip/I20250318.zip']' returned non-zero exit status 1.

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Documents\Programing\TRO\USside\IP\Patents\patent_file.py", line 199, in send_to_nas
    nas.transfer_file_with_scp(local_path=local_path, remote_path=remote_path, to_nas=True)
  File "D:\Documents\Programing\TRO\USside\FileManagement\NAS.py", line 396, in transfer_file_with_scp
    raise RuntimeError(f"SCP failed after {max_retries} attempts. Final error: {e.stderr}") from e
RuntimeError: SCP failed after 3 attempts. Final error: Warning: Permanently added 'jslawny.synology.me' (ED25519) to the list of known hosts.
scp: dest open "/Maidalv/IP TRO Data/IP/Patents/USPTO_Grants/Zip/I20250318.zip": No such file or directory
scp: failed to upload file D:/Documents/Programing/TRO/USside/Documents/IP/Patents/USPTO_Grants/Zip/I20250318.zip to /Maidalv/IP TRO Data/IP/Patents/USPTO_Grants/Zip/I20250318.zip

2025-04-17 17:53:58,454 - __main__ - INFO - Background task NAS_Zip_I20250318.zip completed successfully.
2025-04-17 17:53:58,454 - __main__ - INFO - Background task NAS_Extract_I20250318.zip completed successfully.
2025-04-17 17:53:58,456 - __main__ - INFO - --- Finished processing I20250318.zip in 27.52 seconds. Records: 0, Images: 0 ---
2025-04-17 17:53:58,456 - __main__ - INFO - 
=== Grant Processing Summary (2025-03-15 to 2025-03-22) ===
2025-04-17 17:53:58,456 - __main__ - INFO - Processed 0 grant files.
2025-04-17 17:53:58,456 - __main__ - INFO - Total records processed: 0
2025-04-17 17:53:58,456 - __main__ - INFO - Total images processed: 0
2025-04-17 17:53:58,458 - __main__ - INFO - ==========================================================
2025-04-17 17:53:58,458 - __main__ - INFO - Test processing completed. Records processed: (0, 0)
2025-04-17 17:56:05,917 - __main__ - INFO - Running test processing for 2025-03-15 to 2025-03-22...
2025-04-17 17:56:05,917 - __main__ - INFO - Processing Grant files from 2025-03-15 to 2025-03-22
2025-04-17 17:56:05,917 - __main__ - INFO - 
=== Processing Grant File for Date: 2025-03-18 ===
2025-04-17 17:56:05,918 - __main__ - INFO - --- Starting processing for I20250318.zip (grant) ---
2025-04-17 17:56:05,918 - __main__ - INFO - Attempting download from: https://api.uspto.gov/api/v1/datasets/products/files/PTGRDT/I20250318.zip
2025-04-17 17:56:05,921 - patent_file - INFO - Attempting to download file from https://api.uspto.gov/api/v1/datasets/products/files/PTGRDT/I20250318.zip to D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Zip\I20250318.zip
2025-04-17 17:56:07,522 - patent_file - INFO - Downloading from https://api.uspto.gov/api/v1/datasets/products/files/PTGRDT/I20250318.zip to D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Zip\I20250318.zip
2025-04-17 17:56:07,523 - patent_file - INFO - Download complete for D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Zip\I20250318.zip. File size: 19620 bytes.
2025-04-17 17:56:07,524 - patent_file - INFO - Validating downloaded zip file: D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Zip\I20250318.zip
2025-04-17 17:56:07,657 - patent_file - ERROR - Validation failed: Downloaded file D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Zip\I20250318.zip (size: 19620 bytes) is invalid or corrupt: File is not a zip file
2025-04-17 17:56:07,658 - patent_file - INFO - Deleted invalid downloaded file: D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Zip\I20250318.zip
2025-04-17 17:56:07,659 - __main__ - WARNING - File not found or download failed: https://api.uspto.gov/api/v1/datasets/products/files/PTGRDT/I20250318.zip. Skipping.
2025-04-17 17:56:07,659 - __main__ - INFO - 
=== Grant Processing Summary (2025-03-15 to 2025-03-22) ===
2025-04-17 17:56:07,659 - __main__ - INFO - Processed 0 grant files.
2025-04-17 17:56:07,660 - __main__ - INFO - Total records processed: 0
2025-04-17 17:56:07,660 - __main__ - INFO - Total images processed: 0
2025-04-17 17:56:07,660 - __main__ - INFO - ==========================================================
2025-04-17 17:56:07,661 - __main__ - INFO - Test processing completed. Records processed: (0, 0)
2025-04-17 18:13:20,984 - __main__ - INFO - Running test processing for 2025-03-15 to 2025-03-22...
2025-04-17 18:13:20,985 - __main__ - INFO - Processing Grant files from 2025-03-15 to 2025-03-22
2025-04-17 18:13:20,985 - __main__ - INFO - 
=== Processing Grant File for Date: 2025-03-18 ===
2025-04-17 18:13:20,986 - __main__ - INFO - --- Starting processing for I20250318.tar (grant) ---
2025-04-17 18:13:20,986 - __main__ - INFO - Attempting download from: https://api.uspto.gov/api/v1/datasets/products/files/PTGRDT/2025/I20250318.tar
2025-04-17 18:13:20,989 - patent_file - INFO - Attempting to download file from https://api.uspto.gov/api/v1/datasets/products/files/PTGRDT/2025/I20250318.tar to D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Zip\I20250318.tar
2025-04-17 18:13:22,669 - patent_file - INFO - Downloading from https://api.uspto.gov/api/v1/datasets/products/files/PTGRDT/2025/I20250318.tar to D:\Documents\Programing\TRO\USside\Documents\IP\Patents\USPTO_Grants\Zip\I20250318.tar
